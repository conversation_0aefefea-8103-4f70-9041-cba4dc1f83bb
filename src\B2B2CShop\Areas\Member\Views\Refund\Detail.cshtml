﻿@using B2B2CShop.Entity
@using Pek.Timing
@inject IWorkContext workContext
@model RefundReturn
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/refund2.css");
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
}
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox" style="border: none;">
@*         <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
        <div>></div> *@
        <a href="@T("Index")">@T("账号中心")</a>
        <div>></div>
        <a class="textSelect" href="@Url.Action("Detail")?Id=@Model.Id">@T("退货和退款")</a>
    </div>
    <div class="contaniner flex">
        <aside>
            <div class="asideTitle"><b>@T("退款详情")</b></div>
               <div class="asideImage">
                <img src="@ViewBag.Goods.GoodsImage" alt="">
            </div>
            <div class="asideGoodsTitle"><span>@ViewBag.Goods.GoodsName</span></div>

            <div class="goodsDetails">
                <div class="flex">
                    <div class="goodsLabel">@T("卖家")：</div>
                    <div class="goodsText">@Model.StoreName</div>
                </div>
                <div class="flex">
                    <div class="goodsLabel">@T("订单编号"):</div>
                    <div class="goodsText textSelect">@Model.OrderSn</div>
                </div>
                <div class="flex">
                    <div class="goodsLabel">@T("成交时间"):</div>
                    <div class="goodsText">
                        @{
                            var timespan1 = ViewBag.Order.PaymentTime;
                            var cjtime = UnixTime.ToDateTime(timespan1);
                        }
                        @cjtime
                    </div>
                </div>
                <div class="flex">
                    @*                     <div class="goodsLabel">单价:</div>
                    <div class="goodsText">¥5.66 *1(数量)</div> *@
                </div>
                <div class="flex">
                    <div class="goodsLabel">@T("运费"):</div>
                    <div class="goodsText">@<EMAIL></div>
                </div>
                <div class="flex">
                    <div class="goodsLabel">@T("商品总价"):</div>
                    <div class="goodsText">@<EMAIL></div>
                </div>
            </div>

            <div class="goodsDetails">
                <div class="flex">
                    <div class="goodsLabel">@T("退款编号")：</div>
                    <div class="goodsText">@Model.RefundSn</div>
                </div>
                <div class="flex">
                    <div class="goodsLabel">@T("退款金额"):</div>
                    <div class="goodsText">@<EMAIL></div>
                </div>
                <div class="flex">
                    <div class="goodsLabel">@T("退款原因"):</div>
                    <div class="goodsText">@Model.RefundreturnBuyerMessage</div>
                </div>
                <div class="flex">
                    @*                     <div class="goodsLabel">要求:</div>
                    <div class="goodsText">退运费</div> *@
                </div>
                <div class="flex">
                    <div class="goodsLabel">@T("货物状态"):</div>
                    <div class="goodsText">
                        @if (Model.RefundreturnGoodsState == 3)
                        {
                            @T("未收到")
                        }
                        else if (Model.RefundreturnGoodsState == 4)
                        {
                            @T("已收货")
                        }
                    </div>
                </div>
            </div>
        </aside>

        <div class="content">
            <div class="box" style="padding-bottom: 0;max-height: 19%;">
                @if (Model.RefundreturnSellerState == 1)
                {
                    <div class="box1"><b>@T("等待商家处理")</b></div>
                }
                else if (Model.RefundreturnSellerState == 2)
                {
                    if (Model.RefundreturnGoodsState == 1)
                    {
                        <div class="box1"><b>@T("待买家发货")</b></div>
                        <a onclick="openReturnDialog(@Model.Id)" class="link-btn">@T("去退货")</a>
                    }
                    else if (Model.RefundreturnGoodsState == 2)
                    {
                        <div class="box1"><b>@T("待仓库收货")</b></div>
                    }
                    else if (Model.RefundreturnGoodsState == 3)
                    {
                        <div class="box1"><b>@T("仓库未收到")</b></div>
                    }
                    else if (Model.RefundreturnGoodsState == 4)
                    {
                        <div class="box1"><b>@T("仓库已收货")/@T("待商家退款")</b></div>
                    }

                }
                else if (Model.RefundreturnSellerState == 3)
                {
                    <div class="box1"><b>@T("商家已拒绝")</b></div>
                }
                else if (Model.RefundreturnSellerState == 4)
                {
                    <div class="box1"><b>@T("已退款")</b></div>
                    <div class="box2 text2">@T("退款成功时间") @(UnixTime.ToDateTime(Model.RefundreturnSellerTime))</div>
                }
            </div>
            <div class="box" style="font-size: .9vw;padding-bottom: 0;max-height: 18%;">
                <div class="text1">
                    <span>@T("退款总金额"):</span>
                    <b class="red" style="margin-left: 5px;font-size: 1vw;">@<EMAIL></b>
                </div>
                <div class="box2 text2">@T("退回") @ViewBag.Order.PaymentCode: @<EMAIL></div>
            </div>

            <div class="box sellerBox">
                <div>@T("协商历史")</div>
                <div style="display: flex; flex-direction: column;">
                    @if (Model.RefundreturnSellerState > 1)
                    {
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            @if (Model.RefundreturnSellerState == 3)
                            {
                                <div>@T("商家已经拒绝了申请")</div>
                            }
                            else if (Model.RefundreturnSellerState != 1)
                            {
                                <div>@T("商家已经同意了申请")</div>
                            }
                            <div class="date">@(UnixTime.ToDateTime(Model.RefundreturnSellerTime))</div>
                        </div>
                    }
                    @if (Model.RefundreturnGoodsState > 1)
                    {
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            @if (Model.RefundreturnGoodsState == 3)
                            {
                                <div>@T("仓库未收到货")</div>
                            }
                            else if (Model.RefundreturnGoodsState == 4)
                            {
                                <div>@T("仓库已收货")</div>
                            }
                            <div class="date">@(Model.RefundreturnReceiveTime!=0?UnixTime.ToDateTime(Model.RefundreturnReceiveTime):"")</div>
                        </div>
                    }
                    @if (Model.RefundreturnSellerState == 4)
                    {
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <div>@T("退款成功")</div>
                            <div class="date">@(Model.RefundTime!=0?UnixTime.ToDateTime(Model.RefundTime):"")</div>
                        </div>
                    }
                </div>
            </div>
            @*  <div class="box sellerBox">
                <div></div>
                <div style="display: flex;">
                    <div class="avatarBox">
                        <img src="../../images/icons/morentouxiang.png" alt="">
                    </div>
                    <div class="textBox">
                        <div>y**y1233</div>
                        <div>标题： 卖家已经同意了申请</div>
                        <div>内容： 退款成功</div>
                        <div>要求:  退运费</div>
                    </div>
                    <div class="date">2021-10-29 11:11:11</div>
                </div>
            </div> *@
        </div>
    </div>
</div>
<script>
function openReturnDialog(refundId) {
    layer.open({
        type: 2,
        title: '@T("退货处理")',
        area: ['500px', '450px'],
        fixed: false,
        maxmin: true,
        content: '@Url.Action("Return")?id=' + refundId,
        scrollbar: false,
        shadeClose: true,
    });
}
</script>