@using B2B2CShop.Dto
@using B2B2CShop.Entity
@inject IWorkContext workContext
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/modulesSearch.css");
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
}
<style>
    .layui-laypage a,
    .layui-laypage span {
    font-size: 14px;
    }
</style>
<body>
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <a class="textSelect" href="/Search/Index">@T("商品列表")</a>
        </div>
        <!-- 正片开始 -->
        <div class="title"></div>
        <div class="filterBox">
            <!-- table -->
            <div style="background-color: white;">
                <table class="layui-table" lay-skin="line" style="background-color: white;">
                    <colgroup>
                        <col style="width: 40%;">
                        <col style="width: 15%;">
                        <col style="width: 15%;">
                        <col style="width: 15%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                        <tr style="background-color: var(--text-color4);">
                            <th>@T("型号")/@T("品牌")/@T("封装")</th>
                            <th>@T("库存")</th>
                            <th>@T("阶梯价格")</th>
                            <th>@T("货期")</th>
                            <th>@T("操作")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (GoodsDto item in Model.GoodsList)
                        {
                            <tr>
                                <td>
                                    <div class="goodsInfo">
                                        <div class="flex"
                                        style="width: 50%;flex-direction: column;min-width: fit-content;">
                                            <a href="@Url.Action("Index","Goods")?goodsId=@item.Id">
                                                <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" alt="@T("商品图片")"
                                                class="lazy-load"
                                                style="width: 100%;margin-top: auto;">
                                            </a>
                                            @if (item.IsWish)
                                            {
                                                <div style="margin-top: auto;cursor: pointer;" id="wish_@(item.Id)" onclick="delWishlist('@item.Id')">
                                                    <i class="iconfont icon-aixin" style="color: var(--blue-deep)"></i>
                                                    @T("取消心愿单")
                                                </div>
                                            }
                                            else
                                            {

                                                <div style="margin-top: auto;cursor: pointer;" id="wish_@(item.Id)"  onclick="addWishlist('@item.Id')">
                                                    <i class="iconfont icon-aixin2" style="color: var(--blue-deep)"></i>
                                                    @T("加入心愿单")
                                                </div>
                                            }

                                        </div>
                                        <div style="width: 60%;margin-left: 10%;">
                                            <a href="@Url.Action("Index","Goods")?goodsId=@item.Id">
                                                <div> <span class="name">@item.Name</span> </div>
                                                <div>@T("分类")：<span class="name">@item.GoodsClassName</span></div>
                                                <div>@T("制造商"): <span class="name">@item.CompanyName</span> </div>
                                                <div>@T("封装")：</div>
                                                <div class="">
                                                    @T("描述")：<span class="name">@item.AdvWord</span>
                                                </div>
                                            </a>
                                            @*                                             <div class="pointer" style="padding-top: 1vw;">
                                                <i class="iconfont icon-pdf" style="color: var(--blue-deep)"></i>
                                                @T("规格书")
                                            </div> *@
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @foreach (WareHouseMaterialDto temp in item.WareHouseMaterials)
                                    {
                                        <div class="textOver">@temp.WareHouseName: @temp.Quantity </div>
                                    }
                                </td>
                                <td>
                                    @foreach (MaterialTieredPrice prices in item.MaterialTieredPrices)
                                    {
                                        <div>@prices.MinQuantity+: @<EMAIL></div>
                                    }
                                </td>
                                <td>
                                    @if (item.WareHouseMaterials.Count > 0 && item.WareHouseMaterials.Sum(e => e.Quantity) > 0)
                                    {
                                        <div class="layui-form" style="">
                                            <div><span class="layui-bg-gray">@T("仓库现货")</span></div>
                                            <div>@T("现在下单")</div>
                                            <div class="textOver">@item.DeliveryTime</div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="layui-form" style="">
                                            <div><span class="layui-bg-gray">@T("需订货")</span></div>
                                            <div class="textOver">@item.DeliveryDate</div>
                                        </div>
                                    }
                                </td>
                                <td>
                                    <div>@T("合计")：<span class="money" id="@item.MaterialId">@(symbolLeft+0)</span> </div>
                                    <div class="layui-form-item">
                                        @if (item.WareHouseMaterials.Count > 0 && item.WareHouseMaterials.Sum(e => e.Quantity) > 0)
                                        {
                                            <div class="layui-input-group">
                                                <input type="number" class="layui-input @item.Id" style="max-width: 100px;" min="1" id="@(item.MaterialId + "_" + item.GoodsPrice.ToString())" data-id="@item.Id" data-tiecount="@item.MaterialTieredPrices.Count">
                                                @if (item.MaterialIds.Count > 1)
                                                {
                                                <div class="layui-input-split layui-input-suffix bgSelect" style="cursor: pointer;" onclick="OpenSkuInfo('@item.Id')">
                                                    @T("购物车")
                                                </div>
                                                }
                                                else
                                                {
                                                <div class="layui-input-split layui-input-suffix bgSelect" style="cursor: pointer;" onclick="addCart('@item.Id')">
                                                    @T("购物车")
                                                </div>
                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="layui-input-group">
                                                <input type="number" class="layui-input @item.Id" style="max-width: 100px;" min="1" id="@(item.MaterialId + "_" + item.GoodsPrice.ToString())" data-id="@item.Id" data-tiecount="@item.MaterialTieredPrices.Count">
                                                <div class="layui-input-split layui-input-suffix bgSelect" style="cursor: pointer; opacity:0.5" disabled>
                                                    @T("购物车")
                                                </div>
                                            </div>
                                            @if (Language?.UniqueSeoCode == "en")
                                            {
                                                <button type="button" class="layui-btn layui-btn-primary textSelect" style="width:167px;margin-top:10px">@T("到货通知")</button>
                                            }
                                            else
                                            {
                                                <button type="button" class="layui-btn layui-btn-primary textSelect" style="width:144px;margin-top:10px">@T("到货通知")</button>
                                            }    
                                        }
                                    </div>
@*                                         <div class="flex" style="justify-content: left;">
                                            <div>@T("起订量"):1</div>
                                            <div style="margin-left: 1vw;">@T("增 量"):1</div>
                                        </div> *@
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

            </div>
            <div id="pagingBox"></div>
        </div>
        <script>
            function select(dom) {
                const type = dom.getAttribute("data-type");
                const parentDom = dom.parentNode;
                $(parentDom).children().attr('class', '')
                // console.log(parentDom,$(parentDom));
                if (type == 0) {
                    dom.className = "bgSelect";
                } else if (type == 1) {
                    dom.className = "bgSelect";
                } else if (type == 2) {
                    dom.className = "bgSelect";
                } else if (type == 3) {
                    dom.className = "bgSelect";
                }
            }
            layui.use(function () {
                var laypage = layui.laypage;
                laypage.render({
                    elem: 'pagingBox',
                    count: @Model.Total, // 数据总数
                    limit: @(Model.limit > 0 ? Model.limit : 10), // 每页显示条数
                    limits: [5,10,20,50,100], // 每页条数的选择项
                    curr: @(Model.page > 0 ? Model.page : 1), // 当前页码
                    groups: 5, // 连续显示页码个数
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 自定义布局
                    theme: '#2C79E8', // 自定义主题色
                    prev: '@T("上一页")',
                    next: '@T("下一页")',
                    first: '@T("首页")',
                    last: '@T("尾页")',
                    countText: ['@T("共") ',' @T("条")'],
                    skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                    limitTemplet: function(item) {
                        return item + ' @T("条/页")';
                    },
                    jump: function(obj, first) {
                        // 首次不执行（首次加载时不跳转）
                        if (!first) {
                            // 创建一个隐藏的表单来提交所有参数
                            var $form = $('<form></form>');
                            $form.attr('action', '/Search/Index');
                            $form.attr('method', 'get');
                            $form.css('display', 'none');

                            // 添加页码参数
                            $form.append('<input type="hidden" name="page" value="' + obj.curr + '" />');

                            // 添加每页条数参数
                            $form.append('<input type="hidden" name="limit" value="' + obj.limit + '" />');

                            // 添加搜索关键字
                            var searchKey = "@Model.key";
                            if (searchKey && searchKey !== "") {
                                $form.append('<input type="hidden" name="key" value="' + searchKey + '" />');
                            }

                            // 添加复选框状态
                            var inventory = @(Model.inventory ? "true" : "false");
                            var roSh = @(Model.roSh ? "true" : "false");

                            $form.append('<input type="hidden" name="inventory" value="' + inventory + '" />');
                            $form.append('<input type="hidden" name="roSh" value="' + roSh + '" />');

                            // 将表单添加到文档中并提交
                            $('body').append($form);
                            $form.submit();
                        }
                    }
                });
            });
        </script>
</body>
<script src="/public/script/lazyload.js"></script>
<script asp-location="Footer">
window.addEventListener('load', function() {
    window.lazyLoadImages();
});
    // 页面加载时更新头部搜索框内容
    $(document).ready(function() {
        // 获取当前页面的搜索关键字
        var searchKey = "@Model.key";

        // 设置头部搜索框的值
        if (searchKey && searchKey !== "") {
            $("#headerkey").val(searchKey);
        }

        // 设置搜索类型为商品(1)
        $("#id_select2_demo1").val("1").trigger('change');

        // 设置复选框状态
        var inventory = @(Model.inventory ? "true" : "false");
        var roSh = @(Model.roSh ? "true" : "false");

        if (inventory) {
            $("#goods1").prop("checked", true);
        }

        if (roSh) {
            $("#goods2").prop("checked", true);
        }
    });

    function queryGoodsList()
    {
        var inventory = $("#goods1").is(":checked");
        var roSh = $("#goods2").is(":checked");
        var key = $("#headerkey").val();
        var cId = '0';
        var selects = document.getElementById("container") ? document.getElementById("container").getElementsByTagName("select") : [];
        var result = [];
        for(var i = 0; i < selects.length;i++)
        {
            var selectId = selects[i].id;
            var selectVal = selects[i].value;
            if(selectVal == "请选择"){
                continue;
            }
                result.push({
                    id: selectId,
                    value: selectVal
                });
        }
        var jsonString = JSON.stringify(result);
           // 构造查询字符串
    var queryParams = {
        cId: cId,
        key: key,
        inventory: inventory,
        roSh: roSh,
        page: 1,
        limit: 3
    };

    // 将查询参数对象转换为查询字符串
    var queryString = Object.keys(queryParams).map(function(key) {
        return encodeURIComponent(key) + '=' + encodeURIComponent(queryParams[key]);
    }).join('&');

            window.location.href = '@Url.Action("Index")' + '?' + queryString;

    }


    @* 加入心愿清单 *@
    function addWishlist(id)
    {
        const wishlistBtn = document.querySelector("#wish_"+id);
        wishlistBtn.disabled = true;
        $.post('@Url.Action("AddWishlist", "CubeHome")',{goodsId:id},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                wishlistBtn.innerHTML = `<i class="iconfont icon-aixin pointer" style="color: var(--blue-deep);"></i> @T("取消心愿单")`;
                wishlistBtn.onclick = function() {
                    delWishlist(id);
                };
            }
            wishlistBtn.disabled = false;
        }).fail(function() {
            wishlistBtn.disabled = false; // 请求失败时也启用
        });
    }
    @* 取消心愿清单 *@
    function delWishlist(id)
    {
        const wishlistBtn = document.querySelector("#wish_"+id);
        wishlistBtn.disabled = true;
        $.post('@Url.Action("DelWishlist", "CubeHome")',{goodsId:id},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                wishlistBtn.innerHTML = `<i class="iconfont icon-aixin2 pointer" style="color: var(--blue-deep);"></i> @T("加入心愿单")`;
                wishlistBtn.onclick = function() {
                    addWishlist(id);
                };
            }
        })
    }

    //加入购物车
    function addCart(GoodsId)
    {

        var GoodsNum = parseInt($("."+GoodsId).val());

        $.post("@Url.Action("AddCart", "CubeHome")",{GoodsId,GoodsNum},function(res)
        {
            layui.layer.msg(res.msg);
            if (res.success) {
                // 如果添加成功，获取当前购物车数量
                if (typeof window.updateCartCount === 'function') {
                    window.updateCartCount(res.data);
                }
            }
        })
    }
    //选择SKU加入购物车
    function OpenSkuInfo(GoodsId)
    {
        var GoodsNum = parseInt($("."+GoodsId).val());
        if  (isNaN(GoodsNum) || GoodsNum <= 0) {
            layui.layer.msg('@T("商品数量必须大于0")');
            return;
        }
        layui.layer.open({
            type: 2, // iframe
            title: '@T("选择商品属性")',
            area: ['700px', '400px'],
            shadeClose: true,
            content: '/CubeHome/SkuSelector?goodsId=' + GoodsId + '&num=' + GoodsNum,
        });
    }
    ///获取弹窗参数更新购物车
    function getChildrenData(res)
    {
         if (res && res.success && typeof window.updateCartCount === 'function') {
            window.updateCartCount(res.data); // 更新购物车数量
         }
    }
    $("input[type='number']").on('input',function()
    {
        var quantity = parseInt($(this).val());

        var Ids = $(this).attr("id").split("_");
        var tieCount = parseInt($(this).data("tiecount"), 10);
        var pricePerItem = parseFloat(Ids[1]);

        if(tieCount>1){
            $.post('@Url.Action("GetTreTieredPrices", "Goods")', { materialId: Ids[0], number: quantity }, function (res) {
                if (res.success) {
                    var pricePerItem = parseFloat(Ids[1]);
                    if(res.data.Price!=0){
                        pricePerItem=res.data.Price;
                        // 检查是否为NaN
                        if (!isNaN(quantity) && !isNaN(pricePerItem)) {
                           var totalAmount = quantity * pricePerItem;
                           $("#" + Ids[0]).text('@symbolLeft' + totalAmount.toFixed(2));
                        } else {
                           // 如果有NaN，重置显示或显示错误信息
                           $("#" + Ids[0]).text('@symbolLeft'+'0');
                        }
                    }
                }else{
                    var pricePerItem = parseFloat(Ids[1]);
                     // 检查是否为NaN
                    if (!isNaN(quantity) && !isNaN(pricePerItem)) {
                       var totalAmount = quantity * pricePerItem;
                       $("#" + Ids[0]).text('@symbolLeft' + totalAmount.toFixed(2));
                    } else {
                        // 如果有NaN，重置显示或显示错误信息
                        $("#" + Ids[0]).text('@symbolLeft'+'0');
                    }
                }
            });
        }else{
            var pricePerItem = parseFloat(Ids[1]);
             // 检查是否为NaN
            if (!isNaN(quantity) && !isNaN(pricePerItem)) {
               var totalAmount = quantity * pricePerItem;
               $("#" + Ids[0]).text('@symbolLeft' + totalAmount.toFixed(2));
            } else {
                // 如果有NaN，重置显示或显示错误信息
                $("#" + Ids[0]).text('@symbolLeft'+'0');
            }
        }
    })

</script>