﻿@using B2B2CShop.Entity
@using Pek.Timing
@inject IWorkContext workContext
@{
    ViewBag.LeftMenu = "Orders";
    ViewBag.LeftChileMenu = "Orders";
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-sellerorder-page");
    IList<OrderGoods> goodsList = Model.ordergoodsList;
    var lId = workContext.WorkingCurrency.Id;

}
@await Html.PartialAsync("_Left")
<div class="seller_main">
    <div class="seller_right">
        <div class="seller_items">
            <ul>
                <li class="@(Model.state == 0 ? "current" : "")"><a href="@Url.Action("Index", new { state = 0 })" ">@T("所有订单")</a></li>
                <li class="@(Model.state == 1 ? "current" : "")"><a href="@Url.Action("Index", new { state = 1 })">@T("等待付款")</a></li>
                <li class="@(Model.state == 2 ? "current" : "")"><a href="@Url.Action("Index", new { state = 2 })">@T("等待发货")</a></li>
                <li class="@(Model.state == 3 ? "current" : "")"><a href="@Url.Action("Index", new { state = 3 })">@T("已经发货")</a></li>
                <li class="@(Model.state == 4 ? "current" : "")"><a href="@Url.Action("Index", new { state = 4 })">@T("已经完成")</a></li>
                <li class="@(Model.state == 5 ? "current" : "")"><a href="@Url.Action("Index", new { state = 5 })">@T("已经取消")</a></li>
            </ul>
        </div>
        <div class="p20">
            <form method="get" action="" target="_self">
                <input type="hidden" name="state" value="@Model.state" />
                <input type="hidden" name="limit" value="@Model.limit" />
                <table class="search-form">
                    <tr>
                        <td>&nbsp;</td>
                        <th>@T("下单时间")</th>
                        <td class="w240">
                            <input type="text" class="text w70" name="buyStratDate" id="buyStratDate" value="@(Model.buyStratDate > DateTime.MinValue ? Model.buyStratDate.ToString("yyyy-MM-dd") : "")" />
                            <label class="add-on"><i class="iconfont">&#xe8d6;</i></label>&nbsp;&#8211;&nbsp;
                            <input id="buyEndDate" class="text w70" type="text" name="buyEndDate" value="@(Model.buyEndDate > DateTime.MinValue ? Model.buyEndDate.ToString("yyyy-MM-dd") : "")" />
                            <label class="add-on"><i class="iconfont">&#xe8d6;</i></label>
                        </td>
                        <th>@T("买家")</th>
                        <td class="w100"><input type="text" class="text w80" name="buyerName" value="@Model.buyerName" /></td>
                        <th>@T("订单编号")</th>
                        <td class="w160"><input type="text" class="text w150" name="orderSn" value="@Model.orderSn" /></td>
                        <td class="w70 tc">
                            <input type="submit" class="submit" value="@T("搜索")" />
                        </td>
                    </tr>
                </table>
            </form>
            <table class="dssc-default-table order">
                <thead>
                    <tr>
                        <th class="w40 tc"></th>
                        <th colspan="2">@T("商品")</th>
                        <th class="w200">@T("单价（元）")</th>
                        <th class="w100">@T("数量")</th>
                        <th class="w200">@T("买家")</th>
                        <th class="w200">@T("订单金额")</th>
                        <th class="w200">@T("交易状态")</th>
                        <th class="w200">@T("交易操作")</th>
                    </tr>
                    <tr>
                        <td class="w40 tc"><input type="checkbox" id="all" class="checkall" /></td>
                        <td colspan="8">
                            <label for="all" class="ml10">@T("全选")</label>
                            @*  <a href="javascript:void(0);" class="dssc-btn-mini" dstype="batch" data-param="{urls:'/home/<USER>/batch_send.html', sign:'send'}"><i></i>@T("设置发货")</a> *@
                            <a href="javascript:void(0);" class="dssc-btn-mini" dstype="batch" data-param="{urls:'/home/<USER>/print_order.html', sign:'print'}"><i></i>@T("打印发货单")</a>
                        </td>
                    </tr>
                </thead>
                <tbody>
                    @foreach (Order orderitem in Model.list)
                    {
                        OrderCommon orderCommon = OrderCommon.FindById(orderitem.Id);
                        <tr>
                            <td colspan="20" class="sep-row"></td>
                        </tr>
                        <tr>
                            <th class="tc"><input type="checkbox" class="checkitem tc" value="6" /></th>
                            <th colspan="20">
                                <span class="ml10">
                                    @T("订单编号")：<em>@orderitem.OrderSn</em>
                                    @orderitem.OrderFrom
                                </span>
                                <span>@T("下单时间")：<em class="goods-time">@(UnixTime.ToDateTime(orderitem.AddTime).ToString("yyyy-MM-dd HH:mm:ss"))</em></span>
                                @if (orderitem.PaymentTime > 0)
                                {
                                    <span>@T("支付时间")：<em class="goods-time">@(UnixTime.ToDateTime(orderitem.PaymentTime).ToString("yyyy-MM-dd HH:mm:ss"))</em></span>
                                }
                                <span class="fr mr5">
                                    <a href="" class="dssc-btn-mini" target="_blank" title="@T("打印发货单")">
                                        <i class="iconfont">&#xe7ca;</i>@T("打印发货单")
                                    </a>
                                </span>
                            </th>
                        </tr>

                        int rowCount = orderitem.OrderGoodsList.Count;
                        int rowspanNum = rowCount >= 2 ? 2 : rowCount;
                        for (int i = 0; i < rowCount; i++)//遍历渲染商品
                        {
                            OrderGoods goodsitem = orderitem.OrderGoodsList[i];
                            <tr class="@(i > 1 ? "row-display" : "")" name="tr@(orderitem.Id)">
                                <td class="bdl"></td>
                                <td class="w70"><div class="dssc-goods-thumb"><a href="@Url.Action("Index", "Goods", new { Area = "", goodsId = goodsitem.GoodsId })" target="_blank"><img src="@goodsitem.ImagePath" /></a></div></td>
                                <td class="tl">
                                    <dl class="goods-name">
                                        <dt>
                                            <a target="_blank" href="@Url.Action("Index", "Goods", new { Area = "", goodsId = goodsitem.GoodsId })">@goodsitem.GoodsName</a>
                                        </dt>
                                        <dd>
                                            <p>@goodsitem.SkuDetail?.SpecValueDetail(lId)</p>
                                        </dd>
                                    </dl>
                                </td>
                                <td>@goodsitem.SymbolLeft @goodsitem.CurGoodsPayPrice</td>
                                <td>@goodsitem.GoodsNum</td>
                                @if (goodsitem.Id == goodsList.Where(x => x.OrderId == orderitem.Id).First().Id)
                                {
                                    <td class="bdl" rowspan="@rowspanNum">
                                        <div class="buyer">
                                            @orderitem.BuyerName<p member_id="3">
                                            </p>
                                            <div class="buyer-info">
                                                <em></em>
                                                <div class="con">
                                                    <h3><i></i><span>@T("联系信息")</span></h3>
                                                    <dl>
                                                        <dt>@T("姓名")：</dt>
                                                        <dd>@orderCommon?.ReciverName</dd>
                                                    </dl>
                                                    <dl>
                                                        <dt>@T("电话")：</dt>
                                                        <dd>@orderCommon?.Phone</dd>
                                                    </dl>
                                                    <dl>
                                                        <dt>@T("收货地址")：</dt>
                                                        <dd>@orderCommon?.FullAddress</dd>
                                                    </dl>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="bdl" rowspan="@rowspanNum">
                                        <p class="dssc-order-amount">@orderitem.SymbolLeft @orderitem.CurOrderAmount</p>
                                        <p class="goods-freight">
                                            (@if (orderitem.CurShippingFee == 0)
                                            {
                                                @T("免运费")
                                            }
                                                                else
                                            {
                                                @T("含运费：")
                                                @orderitem.SymbolLeft @orderitem.CurShippingFee
                                            }
            )
                                        </p>
                                        @if (new int[] { 30, 35, 40 }.Contains(orderitem.OrderState))
                                        {
                                            <p class="goods-freight">@T("实际支付运费：")@orderCommon.ActualSymbolLeft  @orderCommon.ActualShippingFee</p>
                                        }
                                        <p class="goods-pay" title="@T("支付方式：在线付款")">@T("在线付款")</p>
                                    </td>
                                    <td class="bdl bdr" rowspan="@rowspanNum">
                                        <p>
                                            @if (orderitem.RefundState == 0)
                                            {
                                                switch (orderitem.OrderState)
                                                {
                                                    case 0:
                                                        <span style="color:rgb(255, 0, 0)">@T(orderitem.OrderStateName)</span>
                                                        break;
                                                    case 10:
                                                    case 14:
                                                    case 15:
                                                        <span style="color:#36C">@T(orderitem.OrderStateName)</span>
                                                        break;
                                                    case 20:
                                                        <span style="color:rgb(32, 7, 170)">@T(orderitem.OrderStateName)</span>
                                                        break;
                                                    case 30:
                                                    case 35:
                                                        <span style="color:rgb(0, 255, 221)">@T(orderitem.OrderStateName)</span>
                                                        break;
                                                    case 40:
                                                        <span style="color:rgb(0, 255, 34)">@T(orderitem.OrderStateName)</span>
                                                        break;
                                                    default:
                                                        <span style="color:rgb(177, 177, 177)">@T(orderitem.OrderStateName)</span>
                                                        break;
                                                }
                                            }
                                            else
                                            {
                                                <span style="color:rgb(255, 0, 0)">@T("关闭交易")</span>
                                            }
                                        </p>
                                        <p>@* <a href="/home/<USER>/show_order.html?order_id=6" target="_blank">@T("订单详情")</a> *@</p>
                                        <p>
                                        </p>
                                    </td>
                                    <td class="bdl bdr" rowspan="@rowspanNum">
                                        @switch (orderitem.OrderState)
                                        {
                                            case 10:
                                                <p><a href="javascript:void(0)" class="dssc-btn-mini dssc-btn-green mt10" dialog_id="seller_order_adjust_fee" id="order@(orderitem.Id)_action_adjust_fee" data-id="@orderitem.Id" data-sn="@orderitem.OrderSn" data-buyer="@orderitem.BuyerName" data-amount="@orderitem.GoodsAmount"><i class="iconfont">&#xe731;</i>@T("修改价格")</a></p>
                                                break;
                                        }
                                    </td>
                                }

                            </tr>
                        }
                        if (rowCount > 2)
                        {
                            <tr class="bd-line">
                                <th colspan="20">
                                    <span class="ml10">@T("共")<em>@rowCount</em>@T("件商品")</span>
                                    <span>
                                        <a href="javascript:void(0)" class="btn-green" onclick="rowShow(@orderitem.Id,@rowCount,true)">
                                            <i class="iconfont">&#xe73a;</i>
                                            @T("展开")
                                        </a>
                                    </span>
                                    <span style="display:none">
                                        <a href="javascript:void(0)" class="btn-green" onclick="rowShow(@orderitem.Id,@rowCount,false)">
                                            <i class="iconfont">&#xe738;</i>
                                            @T("收起")
                                        </a>
                                    </span>
                                </th>
                            </tr>
                        }
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="7">
                            <div class="pagination">
                                <ul class="pagination">
                                    @Html.Raw(Model.PageHtml)
                                </ul>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <script type="text/javascript">
                $(function () {
                    $('a[dstype="batch"]').click(function () {
                        if ($('.checkitem:checked').length == 0) {    //没有选择
                            layer.alert('@T("请选择需要操作的记录")');
                            return false;
                        }
                        var _items = '';
                        $('.checkitem:checked').each(function () {
                            _items += $(this).val() + ',';
                        });
                        _items = _items.substr(0, (_items.length - 1));

                        var data_str = '';
                        eval('data_str = ' + $(this).attr('data-param'));

                        if (data_str.sign == 'send') {
                            ajax_form('ajax_send', '@T("设置发货")', data_str.urls + '?order_id=' + _items + '&inajax=1', '480');
                        } else if (data_str.sign == 'print') {
                            window.open(data_str.urls + '?order_id=' + _items);
                        } else if (data_str.sign == 'eorder') {
                            indow.open(data_str.urls + '?order_id=' + _items);
                        }
                    });
                    $('#buyStratDate').datepicker({dateFormat: 'yy-mm-dd'});
                    $('#buyEndDate').datepicker({dateFormat: 'yy-mm-dd'});
                    $('.checkall_s').click(function () {
                        var if_check = $(this).prop('checked');
                        $('.checkitem').each(function () {
                            if (!this.disabled)
                            {
                                $(this).prop('checked', if_check);
                            }
                        });
                        $('.checkall_s').prop('checked', if_check);
                    });
                    $('#skip_off').click(function () {
                        url = location.href.replace(/&skip_off=\d*/g, '');
                        window.location.href = url + '&skip_off=' + ($('#skip_off').prop('checked') ? '1' : '0');
                    });
                    $('a[dialog_id="seller_order_adjust_fee"]').click(function() {
                        var $this = $(this);
                        var orderId = $this.data('id');
                        var orderSn = $this.data('sn');
                        var buyerName = $this.data('buyer');
                        var amount = $this.data('amount');

                        layer.open({
                            type: 1,
                            title: '@T("修改价格")',
                            area: ['500px', '300px'],
                            content: `<div class="adjust-price-dialog">
                                        <div class="info-group">
                                            <div class="info-item">
                                                <label>@T("买家")：</label>
                                                <span class="buyer-name">${buyerName}</span>
                                            </div>
                                            <div class="info-item">
                                                <label>@T("订单编号")：</label>
                                                <span class="order-sn">${orderSn}</span>
                                            </div>
                                            <div class="price-input">
                                                <label>@T("修改价格")：</label>
                                                <input type="text" class="text" id="newPrice" value="${parseFloat(amount).toFixed(2)}" />
                                                <span class="unit">@T("元")</span>
                                            </div>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn-submit" onclick="submitAdjustPrice('${orderId}')">@T("确定")</button>
                                            <button type="button" class="btn-cancel" onclick="layer.closeAll()">@T("取消")</button>
                                        </div>
                                    </div>
                                    <style>
                                        .adjust-price-dialog { padding: 20px; }
                                        .info-group { margin-bottom: 20px; }
                                        .info-item { margin-bottom: 15px; line-height: 30px; }
                                        .info-item label { display: inline-block; width: 80px; color: #666; }
                                        .order-sn { color: #19be6b; font-weight: bold; }
                                        .price-input { margin-top: 20px; }
                                        .price-input label { display: inline-block; width: 80px; color: #666; }
                                        .price-input input { width: 150px; height: 32px; padding: 0 10px; border: 1px solid #ddd; border-radius: 4px; }
                                        .price-input .unit { margin-left: 5px; color: #666; }
                                        .btn-group { text-align: center; }
                                        .btn-group button { margin: 0 10px; padding: 8px 25px; border: none; border-radius: 4px; cursor: pointer; }
                                        .btn-submit { background: #2d8cf0; color: #fff; }
                                        .btn-submit:hover { background: #2b85e4; }
                                        .btn-cancel { background: #f7f7f7; color: #666; }
                                        .btn-cancel:hover { background: #e6e6e6; }
                                    </style>`
                        });
                    });
                });
                function submitAdjustPrice(orderId) {
                    var newAmount = $('#newPrice').val();
                    if (!newAmount || isNaN(newAmount) || newPrice <= 0) {
                        layer.msg('@T("请输入有效的价格")', {icon: 2,time:1000,anim: 6});
                        return;
                    }

                    $.post('@Url.Action("UpdateOrderAmount")', { orderId: orderId, newAmount: newAmount }, function(res) {
                        if (res.success) {
                            layer.msg('@T("修改成功")', {icon: 1,time:500});
                            window.location.reload();
                        } else {
                            layer.msg(res.msg || '@T("修改失败")', {icon: 2});
                        }
                    });
                }
                // 行展开/收起功能
                function rowShow(id, rows, isShow) {
                    var trs = $('tr[name="tr' + id + '"]');
                    var firstTr = trs.first();

                    // 显示或隐藏行
                    trs.each(function(index) {
                        if (index > 1) {
                            $(this).toggle(isShow);
                        }
                    });

                    // 更新rowspan值
                    var newRowspan = isShow ? rows : 2;
                    firstTr.find('td[rowspan]').each(function() {
                        $(this).attr('rowspan', newRowspan);
                    });

                    // 切换展开/收起按钮显示
                    $('a[onclick="rowShow(' + id + ',' + rows + ',true)"]').parent().toggle(!isShow);
                    $('a[onclick="rowShow(' + id + ',' + rows + ',false)"]').parent().toggle(isShow);
                }

            </script>
        </div>
    </div>
</div>

