﻿@using B2B2CShop.Entity
@using NewLife.Data
@inject IWorkContext workContext
@inject IManageProvider _provider
@inject IWebHelper _webHelper
@{

    var language = workContext.WorkingLanguage;

    var adminarea = DHSetting.Current.AdminArea.ToLower();

    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/index.css");
    PekHtml.AppendCssFileParts("~/public/modules/swiper/basic/css/idangerous.swiper.css");
    //js
    PekHtml.AppendScriptParts(ResourceLocation.Head,"~/public/modules/swiper/basic/js/idangerous.swiper.min.js");

    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;

    var user = _provider.TryLogin(Context);

    var returnUrl = _webHelper.GetRawUrl(Context.Request);

    var site = SiteInfo.GetDefaultSeo();
}
<style asp-location="true">
    .displayFlex{
    display: flex !important;
    }
    .mainBox2_content_desc {
    display: -webkit-box;
    word-break: break-all;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    margin: 5px 0;
    height: 26px;
    }
    .guessContent_desc {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    margin: 5px 0;
    }
    .tu{
    font-size: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    }

</style>
<script asp-location="Head">

</script>
<div class="mainBox0">
    <div class="mainBox1_container">
        <!-- 轮播图区域 -->
        <div class="mainBox1">
            <div class="mainBox1_left">
                <!-- 一级页面 -->
                <div class="onePage">
                    @foreach (var item in Model.GoodsClassList)
                    {
                        <div data-select="false" id="@item.Id"><a href="/ProductClass?CId=@item.Id"><span style="width: 80%;" class="textOver">@item.Name</span></a><i class="iconfont icon-jiantou2 rightIcon"></i></div>
                    }
                </div>
                <!-- 二级页面 -->
                @foreach (var item1 in Model.GoodsClassList)
                {
                    <div class="hoverPages" id="@("a"+item1.Id)">
                        <div class="twoPage" id="@("b"+item1.Id)">
                            @foreach (var item2 in item1.Child)
                            {
                                <div data-select="false">
                                    <a href="@Url.Action("Index", "Products", new { CId = item2.Id })" ><span style="width: 80%;" class="textOver">@item2.Name</span></a>
                                </div>
                            }
                        </div>
                        <div class="threePage">
                            <p class="goodsMore"><a href="#">@T("更多")<i class="iconfont icon-jiantou2 rightIcon"></i></a></p>
                            @foreach (var item2 in item1.Goods)
                            {
                                <a class="jump2" href="@Url.Action("Index","Goods",new {Area = ""})?goodsId=@item2.Id">
                                    <div class="threePage_contentBox"  title="@item2.Name">
                                        <div class="threePage_contentBox_name">
                                            @item2.Name (@item2.GoodsStorage)
                                        </div>
                                    </div>
                                </a>
                            }    
                        </div>
                    </div>
                }
            </div>
            <div class="mainBox1_center">
                <div>
                    <div class="mainBox1_center_left">

                        <!--轮播图设置-->
                        <div class="swiper-container">
                            <div class="swiper-wrapper">
                                @if (Model.Slideshow.Count > 0)
                                {
                                    foreach (Advertising item in Model.Slideshow)
                                    {
                                        <div class="swiper-slide" style="width:@(Model.Advposition.Width)px;height:@(Model.Advposition.Height)px; background:@item.BgColor">
                                            <a href="@(item.Link.IsNullOrEmpty()?"javascript:void(0);":item.Link)" title="@item.Title" @(item.Link.IsNullOrEmpty() ? "" : "target ='_blank'") onclick="addClickNumber('@item.Id')">
                                                <img src="@item.PicturePath" alt="@item.Title" style=" width:@(Model.Advposition.Width)px;height:@(Model.Advposition.Height)px;">
                                            </a>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <div class="swiper-slide"><img src="~/public/images/icons/denglu-bg.png" alt=""></div>
                                }
                            </div>
                            <!-- 如果需要分页器 -->
                            <div class="pagination"></div>

                            <!-- 如果需要导航按钮 -->
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-button-next"></div>

                            <!-- 如果需要滚动条 -->
                            <div class="swiper-scrollbar"></div>
                        </div>
                        <!-- <img src="~/public/images/icons/denglu-bg.png" style="width: 98%;height: 100%;object-fit: cover;"> -->
                    </div>

                    <div class="mainBox1_center_right">
                        @if (user == null || !user.Enable) { <!-- 未登录 -->

                            <div class="isLogin">
                                <div style="padding-top: .7vw;">@T("Hi~ 欢迎来到{0}！", site.SiteName!)</div>
                                <div style="padding-top: 1.2vw" class="flex">
                                    <button class="loginBtn button"> 
                                        <a href="@_PekUrlHelper.PekRouteUrl(Url, "MuliLogin", Language?.UniqueSeoCode)" style="color: white;" title="@T("登录")">@T("登录")</a>
                                    </button>
                                    <button class="registerBtn button">
                                        <a href="@_PekUrlHelper.PekRouteUrl(Url, "MuliRegister", Language?.UniqueSeoCode)" style="color: white;" title="@T("注册")">@T("注册")</a>
                                    </button>
                                </div>
                            </div>
                        }
                        @if (user != null && user.Enable) {<!-- 登陆 -->
                            XTrace.WriteLine($"Index User  {user.Name}");
                            <div class="isLogin userInfoBox2" data-login="true">
                                <div class="acountHead flex isLogin" data-login_flex="true" style="padding-bottom: 10px ;">
                                    <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="pointer" style="width: 25%;display: block;margin-right: 5px;">
                                        <img src="~/public/images/icons/morentouxiang.png" alt="@T("用户头像")">
                                    </a>
                                    <div class="textOver" style="font-size: 13px;margin-left: 5px;">
                                        <div class="flex pointer">
                                            <div class="textOver">
                                                @if (ValidateHelper.IsMobile(user.Name))
                                                {
                                                    <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="textSelect"> @DesensitizedUtil.Mobile(user.Name); </a>

                                                }
                                                else if (ValidateHelper.IsEmail(user.Name))
                                                {
                                                    <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="textSelect"> @DesensitizedUtil.Email(user.Name); </a>

                                                }
                                                else
                                                {
                                                    <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="textSelect"> @DesensitizedUtil.ReplaceWithSpecialChar(user.Name); </a>

                                                }
                                            </div>
                                            <div style="margin:0 auto 0 5px;"><a href="#">@T("您好！")</a> </div>
                                        </div>
                                        <div class="textOver" style="margin-top: 0.2vw;white-space: nowrap;text-align: left;">@T("Hi~ 欢迎来到{0}！", site.SiteName!)
                                        </div>
                                    </div>
                                </div>
                                <div class="flex xxbox">
                                    <div>
                                        <a href="/Member/Orders/Index?orderState=10">
                                            <div class="iconfont iconSize28 icon-creditcard"></div>
                                            <div>@T("待支付") </div>
                                        </a>
                                    </div>
                                    <div>
                                        <a href="/Member/Orders/Index?orderState=20">
                                            <div class="iconfont iconSize28 icon-wuliuxinxi"></div>
                                            <div>@T("未发货")</div>
                                        </a>
                                    </div>
                                    <div>
                                        <a href="/Member/Orders/Index?orderState=30">
                                            <div class="iconfont iconSize28 icon-wuliu"></div>
                                            <div>@T("配送中")</div>
                                        </a>
                                    </div>
                                    <div>
                                        <a href="/Member/Orders/Index?orderState=40">
                                            <div class="iconfont iconSize28 icon-yishouhuo1"></div>
                                            <div>@T("已签收")</div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        }

                        <div class="">
                            <div class="NoticeBoxContainer">
                                <div style="width: 80%;margin-left: 10%;font-weight: bold;text-align: center;">@T("海凌科公告")
                                </div>
                                @* <a class="more">@T("更多")</a> *@
                            </div>
                            <div class="noticeBox">
                                @foreach (var item in Model.Articlelist)
                                {
                                    <div class="textOver">@item.Name</div>
                                }

                            </div>
                        </div>
                        @* <div class="flex advert">
                            <a href="@Url.Action("Login", "Sellers", new { Area = "" })">
                                <img src="~/public/images/icons/gonghuoshang.png" alt="@T("商家入驻")">
                            </a>
                        </div> *@
                    </div>
                </div>
                @* <div class="mainBox1_bottom">
                    <div class="flex" style="justify-content: left;"><img
                        style="margin-left: 1.7em;max-height: 66%;margin-right: .5em;"
                        src="~/public/images/icons/BOM.png" alt="">@T("上传BOM")</div>
                    <div class="flex" style="justify-content: left;"><img
                        style="margin-left: 1.7em;max-height: 66%;margin-right: .5em;"
                        src="~/public/images/icons/RFQ.png" alt="">@T("RFQ询盘")</div>
                    <div class="flex" style="justify-content: left;"><img
                        style="margin-left: 1.7em;max-height: 66%;margin-right: .5em;"
                        src="~/public/images/icons/youhuiquan.png" alt="">@T("优惠劵领取")</div>
                    <div class="flex" style="justify-content: left;"><img
                        style="margin-left: 1.7em;max-height: 66%;margin-right: .5em;"
                        src="~/public/images/icons/shengshu.png" alt="">@T("质量管理体系证书")</div>
                </div> *@
            </div>
        </div>

    </div>

    <div class="mainBox2 flex" style="justify-content: space-around;">
        <div>
            <div class="mainBox2_title flex" style="padding: 10px 20px;font-size: 30px;">
                <a href="">@T("新品推荐")</a>
                <img src="~/public/images/icons/xinpintuijian.png" alt="@T("查看更多")" style="margin-left: auto;cursor: pointer;" onclick="toRouter(this)" data-link="@Url.Action("Index","Search")">
            </div>
            <div class="mainBox2_title" style="padding: 0px 20px;">@* @T("新品限时5折优惠") *@</div>
            <div class="mainBox2_container" id="LatestRecommendation1">
                @foreach (var item in Model.NewGoodsList1)
                {
                    <a class="mainBox2_content_home" href="@Url.Action("Index","Goods")?goodsId=@item.Id">
                        <div>
                            <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" alt="@T("新品图片")" class="lazy-load" style="width: 100%; height: 100%; object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">@item.Name</div>
                        <div class="buy"><span> @item.GoodsBuynum@T("人购买") </span> <i class="layui-icon layui-icon-star-fill"></i><span class="starNum">@item.EvaluationGoodStar</span> </div>
                        <div class="mainBox2_content_price">@<EMAIL></div>
                    </a>
                }
            </div>
        </div>
        <div>
            <div class="mainBox2_title flex" style="padding: 10px 20px;font-size: 30px;">
                <a href="">@T("新品推荐")</a>
                <img src="~/public/images/icons/zhekoupin.png" alt="@T("查看更多")" style="margin-left: auto;cursor: pointer;" onclick="toRouter(this)" data-link="@Url.Action("Index","Search")">
            </div>
            <div class="mainBox2_title" style="padding: 0px 20px;">@* @T("新品限时5折优惠") *@</div>
            <div class="mainBox2_container" id="LatestRecommendation2">
                @foreach (var item in Model.NewGoodsList2)
                {
                    <a class="mainBox2_content_home" href="@Url.Action("Index","Goods")?goodsId=@item.Id">
                        <div>
                            <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" alt="@T("新品图片")" class="lazy-load" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="mainBox2_content_desc">@item.Name</div>
                        <div class="buy"><span>@item.GoodsBuynum@T("人购买")  </span><i class="layui-icon layui-icon-star-fill"></i><span class="starNum">@item.EvaluationGoodStar </span></div>
                        <div class="mainBox2_content_price">@<EMAIL></div>
                    </a>
                }
            </div>
        </div>
    </div>
    <!-- 新品推荐结束 -->
    <!-- 排行榜开始 -->

    <div class="rangeBox">
        <div class="title flex">
            <a href="">@T("排行榜")</a>
            <div class="more" style="font-size: .9vw;margin-right: 50px;"><a href="@Url.Action("Index","Search")">@T("更多")></a></div>
        </div>
        <div class="rangeFunc flex" style="justify-content: left;margin-top: 1vw;">
            <div id="rank1"><a href="javascript:rankSwitch(1)" id="rank2">@T("销量排行榜")</a></div>
            <div id="rank3"><a href="javascript:rankSwitch(2)" id=" rank4">@T("热销排行榜")</a></div>
        </div>
        <div class="rangeContainer" id="rangeContainer1">
            <div class="guessContainer" id="">
                @foreach (var item in Model.GoodsRankList1)
                {
                    <a class="guessContent" href="@Url.Action("Index", "Goods")?goodsId=@item.Id">
                        <div>
                            <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" class="lazy-load" alt="@T("商品图片")" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="guessContent_desc">@item.Name</div>
                        <div class="buy">
                            <span>@item.GoodsBuynum @T("人购买")</span><i class="layui-icon layui-icon-star-fill"></i> <span class="starNum">@item.EvaluationGoodStar</span>
                        </div>
                        <div class="guessContent_price">@<EMAIL></div>
                    </a>
                }
            </div>
        </div>
        <div class="rangeContainer" id="rangeContainer2">
            <div class="guessContainer" id="">
                @foreach (var item in Model.GoodsRankList2)
                {
                    <a class="guessContent" href="@Url.Action("Index", "Goods")?goodsId=@item.Id">
                        <div>
                            <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" class="lazy-load" alt="@T("商品图片")" style="width: 100%;object-fit: contain;">
                        </div>
                        <div class="guessContent_desc">@item.Name</div>
                        <div class="buy">
                            <span>@item.GoodsBuynum @T("人购买")</span><i class="layui-icon layui-icon-star-fill"></i> <span class="starNum">@item.EvaluationGoodStar</span>
                        </div>
                        <div class="guessContent_price">@<EMAIL></div>
                    </a>
                }
            </div>
        </div>
    </div>
    <!-- 排行榜结束 -->


    <!-- 猜你喜欢 mainBox2_container-->
    <div class="guessLikeBox">
        <div class="title flex">
            <a href="">@T("猜你喜欢")</a>
            <div class="more" style="font-size: .9vw;margin-right: 50px;"><a href="@Url.Action("Index", "Search")">@T("更多")></a></div>
        </div>
        <div class="guessContainer" id="GuessWhatYouLike">
            @foreach (var item in Model.LikeList)
            {
                <a class="guessContent" href="@Url.Action("Index","Goods")?goodsId=@item.Id">
                    <div>
                        <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" class="lazy-load" alt="@T("商品图片")" style="width: 100%;object-fit: contain;">
                    </div>
                    <div class="guessContent_desc">@item.Name</div>
                    <div class="buy">
                         <span>@item.GoodsBuynum @T("人购买")</span><i class="layui-icon layui-icon-star-fill"></i> <span class="starNum">@item.EvaluationGoodStar</span>
                    </div>
                    <div class="guessContent_price">@<EMAIL></div>
                </a>
            }
        </div>

    </div>

    <!-- 猜你喜欢结束 -->

    <!-- 供货商品牌开始 -->
@*     <div class="providerBox">
        <div class="title flex">
            <a href="">@T("供货商品牌")</a>
            <div class="more" style="font-size: .9vw;margin-right: 50px;"><a href="/Supplier/Index">@T("更多")></a></div>
        </div>
        <div class="providerContainer">
            @foreach (var item in Model.StoreList)
            {
                <div class="providerContent" >
                    <div>
                        <a href="@Url.Action("ProviderDetail", "Supplier", new { sid = item.Id })">
                            <img src="/public/images/icons/gonghuoshang.png" data-src="@item.Logo" class="lazy-load" alt="@T("供货商品牌")" style="width: 100%;object-fit: contain;">
                        </a>
                    </div>
                </div>
            }
        </div>
    </div> *@
   
</div>
 <!-- 企业员工信息  -->
<div class="employeeBox">
    <div>
        <div class="footer1Content">
            <div style="font-size: 3vw;">@Settings.Current.EstablishmentTime</div>
            <div>@T("多年IOT互联网")</div>
            <div>@T("模组经验")</div>
        </div>
        <div class="footer1Content">
            <div style="font-size: 3vw;">@Settings.Current.FactoryArea</div>
            <div>@T("工厂面积")</div>
        </div>
        <div class="footer1Content">
            <div style="font-size: 3vw;">@Settings.Current.RDEngineer</div>
            <div>@T("研发工程师")</div>
        </div>
        <div class="footer1Content">
            <div style="font-size: 3vw;">@Settings.Current.Employee</div>
            <div>@T("员工")</div>
        </div>
        <div class="footer1Content">
            <div style="font-size: 3vw;">@Settings.Current.Customer</div>
            <div>@T("全球企业用户")</div>
        </div>
    </div>
</div>
 <!-- 侧边功能栏 -->
<div class="aside" style="display:block !important;z-index:9999 !important;">
    <div class="asideContainer">
        <div class="asideContent">
            <a href="@Url.Action("Index", "ShoppingCart", new { area = ""})">
                <img src="~/public/images/icons/gouwuche.png" alt="">
                <div>@T("购物车")</div>
            </a>
        </div>
        <div class="asideContent">
            <a href="@_PekUrlHelper.PekRouteUrl(Url, "MuliHelpHome", Language?.UniqueSeoCode)">
                <img src="~/public/images/icons/bangzhuzhongxin.png" alt="">
                <div>@T("帮助中心")</div>
            </a>
        </div>
        <div class="asideContent">
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <img src="~/public/images/icons/xinxizhongxin.png" alt="">
                <div>@T("账号信息")</div>
            </a>
        </div>
        <div class="asideContent service">
            <img src="~/public/images/icons/zaixiankefu.png" alt="">
            <div>@T("在线客服")</div>
            <!-- serviceBox 作为 service 的子元素 -->
            <div class="serviceBox">
                <div class="serviceBox-header">
                    <span class="serviceBox-title">在线客服热线</span>
                </div>
                <div class="serviceBox-content">
                    <div class="serviceBox-item">
                        <img src="~/public/images/icons/lianxifangshi.png" alt="电话" class="serviceBox-item-icon">
                        <span>0755-********</span>
                    </div>
                    <div class="serviceBox-item">
                        <img src="~/public/images/icons/weiduxinxi.png" alt="邮箱" class="serviceBox-item-icon">
                        <span><EMAIL></span>
                    </div>
                    <div class="serviceBox-section">
                        <div class="serviceBox-section-title">服务时间</div>
                        <div class="serviceBox-time">
                            <span>工作日 9:15-20:30</span>
                            <span>节假日 9:15-18:40</span>
                        </div>
                    </div>
                    <div class="serviceBox-section">
                        <div class="serviceBox-section-title"><a href="@Url.Action("ServicesAndComplaints")">@T("服务投诉")</a></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="asideContent">
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <img src="~/public/images/icons/lishijilu.png" alt="">
                <div>@T("浏览记录")</div>
            </a>
        </div>
        <div class="asideContent" onclick="scrollToTop()">
            <img src="~/public/images/icons/dingbu.png" alt="">
            <div>@T("返回顶部")</div>
        </div>
    </div>
</div>

<script src="/public/script/lazyload.js"></script>
<script asp-location="Footer">
window.addEventListener('load', function() {
    window.lazyLoadImages();
});
    rankSwitch(1);

    function rankSwitch(index){
        if(index === 1){
            $('#rangeContainer1').show();
            $('#rangeContainer2').hide();
            $('#rank1').addClass('textSelect');
            $('#rank2').addClass('textSelect');
            $('#rank3').removeClass('textSelect');
            $('#rank4').removeClass('textSelect');
        }else{
            $('#rangeContainer2').show();
            $('#rangeContainer1').hide();
            $('#rank1').removeClass('textSelect');
            $('#rank2').removeClass('textSelect');
            $('#rank3').addClass('textSelect');
            $('#rank4').addClass('textSelect');
        }
    }

    // 加载图片 - 将data-src的值赋给src属性
    var onePage_dom = $('.onePage');
    var moveInArr = []
    onePage_dom.mouseover(function (e) {
        // 确保我们获取的是直接子元素的ID
        console.log('e => ', e.target.id)
        var targetId = e.target.id;
        if (!targetId || moveInArr.includes(targetId)) return;
        moveInArr.push(targetId);
        
        // 找到对应的二级菜单容器
        var correspondingMenuId = "a" + targetId;
        var correspondingMenu = $("#" + correspondingMenuId);
        // 加载该容器内所有懒加载图片
        setTimeout(function() {
            correspondingMenu.find('img[data-src]').each(function() {
                if (!$(this).attr('src') || $(this).attr('src') === '') {
                    $(this).attr('src', $(this).data('src'));
                }
            });
        }, 100); // 添加小延迟，确保用户真的想查看分类
    });
    // 为所有带 data-href 属性的图标绑定点击事件
    document.querySelectorAll('.iconfont[data-href]').forEach(function(icon) {
        icon.addEventListener('click', function() {
            window.location.href = icon.getAttribute('data-href');
        });
    });

        // 轮播图css
        var mySwiper = new Swiper('.swiper-container', {
            autoplay: 5000,//可选选项，自动滑动
            loop: true,//可选选项，开启循环
            // 如果需要分页器
            pagination: '.pagination',
            paginationClickable: true,
            mousewheelControl: true,
        })

        // 监听鼠标hover事件
        var mainBox1_left_dom = $(".mainBox1_left");
        mainBox1_left_dom.mouseover(function (e) 
        {
              if (e.target.getAttribute("data-select")) 
              {
                    $(e.target).siblings().attr("data-select", "false");
                    e.target.setAttribute("data-select", "true");
                    $(e.target).siblings().each(function() 
                    {
                        let idd = $(this).attr("id");
                        if(idd)
                        {
                            $("#a"+idd).removeClass("displayFlex");
                        }
                    });
                    let tid = $(e.target).attr("id");
                    if(tid)
                    {
                        let yid = "a"+tid;
                        $("#"+yid).addClass("displayFlex");
                    }
              }
        });
        var mainBox1_dom = $(".mainBox1");
        mainBox1_left_dom.mouseleave(function(e)
        {
            $(e.target).closest('.mainBox1_left').find('*').removeClass('displayFlex');
        });

function addClickNumber(id) {
    // 取本地已点击的广告ID列表
    var clicked = JSON.parse(localStorage.getItem('clickedAds') || '[]');
    if (clicked.includes(id)) {
        // 已点击过，不再发送
        return;
    }
    // 发送请求
    $.post('@Url.Action("ClickNumber")', { Id: id });
    // 记录已点击
    clicked.push(id);
    localStorage.setItem('clickedAds', JSON.stringify(clicked));
}


</script>