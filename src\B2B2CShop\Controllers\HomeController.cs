﻿using B2B2CShop.Common;
using B2B2CShop.Dto;
using B2B2CShop.Entity;
using DH.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife.Cube.ViewModels;
using NewLife.Data;
using NewLife.Log;

using Pek;
using Pek.Models;
using Pek.NCube;
using Pek.NCubeUI.MVC.Routing;
using Pek.Seo;
using Pek.Timing;
using System.Collections.Generic;
using System.Dynamic;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Controllers;

/// <summary>主页面</summary>
[DHSitemap(IsUse = true)]
public class CubeHomeController : PekBaseControllerX {
    /// <summary>主页面</summary>
    /// <returns></returns>
    public IActionResult Index()
    {

        ViewBag.Message = "主页面";

        //DHSetting.Current.AllowDynamicRedirection = true;
        //DHSetting.Current.Save();

        dynamic viewModel = new ExpandoObject();

        //var _pek = Pek.Webs.HttpContext.Current.RequestServices.GetService<IPekUrlHelper>();
        //XTrace.WriteLine($"获取数据：{_pek?.PekRouteUrl(Url, "MuliChangeLanguage", WorkingLanguage.UniqueSeoCode, new { langid = 1 })}");



        var goodsList = Goods.FindAll();
        List<Goods> likeList = new();
        if(goodsList.Count > 0)
        {
            for (int i = 0; i < 12; i++)
            {
                Random random = new Random();
                int randomNumber = random.Next(0, goodsList.Count);
                var modal = goodsList[randomNumber];
                likeList.Add(modal);
            }
        }

        viewModel.LikeList = likeList.Select(e => new Goods
        {
            Id = e.Id,
            Name = GoodsLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.LanName ?? e.Name,
            GoodsPrice = (e.GoodsPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal(),
            GoodsStorage = MerchantMaterial.GetQuantityByWIds(e.MaterialIds),
            GoodsImage = AlbumPic.FindByNameAndSId(GoodsLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.LanGoodsImage ?? e.GoodsImage ?? "", e.StoreId)?.Cover ?? "",
            GoodsSalenum = e.GoodsSalenum,
            GoodsBuynum = e.GoodsBuynum,
            EvaluationGoodStar = e.EvaluationGoodStar,
        });

        //供应商品牌
        viewModel.StoreList = Store.FindAll(Store._.State == 1, new PageParameter { PageIndex = 1, PageSize = 20 });

        //文章公告
        viewModel.Articlelist = Article.FindAll(null, new PageParameter { PageIndex = 1, PageSize = 4, Sort = "Id", Desc = true }).Select(e => new Article
        { 
            Id = e.Id,
            Name = (ArticleLan.FindByAIdAndLId(e.Id,WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? e.Name : ArticleLan.FindByAIdAndLId(e.Id, WorkingLanguage.Id)?.Name
        });

        //商品分类
        viewModel.GoodsClassList = GoodsClass.FindAllByTreeLan(WorkingLanguage.Id);

        //销量排行榜
        var goods1 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 1, PageSize = 3, Sort = Goods._.GoodsSalenum, Desc = true }, 1);
        viewModel.GoodsRankList1 = goods1;

        var goods2 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 2, PageSize = 3, Sort = Goods._.GoodsSalenum, Desc = true }, 1);
        viewModel.GoodsRankList2 = goods2;

        var goods3 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 3, PageSize = 3, Sort = Goods._.GoodsSalenum, Desc = true }, 1);
        viewModel.GoodsRankList3 = goods3;

        var goods4 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 4, PageSize = 3, Sort = Goods._.GoodsSalenum, Desc = true }, 1);
        viewModel.GoodsRankList4 = goods4;

        //热销排行榜
        var goods5 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 1, PageSize = 3, Sort = Goods._.GoodsBuynum, Desc = true }, 1);
        viewModel.GoodsRankList5 = goods5;

        var goods6 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 2, PageSize = 3, Sort = Goods._.GoodsBuynum, Desc = true }, 1);
        viewModel.GoodsRankList6 = goods6;

        var goods7 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 3, PageSize = 3, Sort = Goods._.GoodsBuynum, Desc = true }, 1);
        viewModel.GoodsRankList7 = goods7;

        var goods8 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 4, PageSize = 3, Sort = Goods._.GoodsBuynum, Desc = true }, 1);
        viewModel.GoodsRankList8 = goods8;

        //新品推荐
        viewModel.NewGoodsList1 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 1, PageSize = 4, Sort = "Id", Desc = true }, 1);

        viewModel.NewGoodsList2 = Goods.SearchLan(-1, false, true, false, false, "", "", WorkingLanguage.Id, -1, WorkingCurrencies.ExchangeRate, -1, new PageParameter { PageIndex = 2, PageSize = 4, Sort = "Id", Desc = true }, 1);

        //首页轮播图
        viewModel.Advposition = AdvPosition.FindById(1);//首页轮播图位置ID
        viewModel.Slideshow = Advertising.FindAllEnabledByAPIdLan(1, WorkingLanguage.Id);//首页轮播图


        return PekView(viewModel, DHSetting.Current.AllowMobileTemp, DHSetting.Current.AllowLanguageTemp);
    }

    /// <summary>错误</summary>
    /// <returns></returns>
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        // 正式环境中，错误页避免返回500错误码
        HttpContext.Response.StatusCode = 200;

        var model = HttpContext.Items["Exception"] as ErrorModel;
        if (IsJsonRequest)
        {
            if (model?.Exception != null) return Json(500, null, model.Exception);
        }

        return View("Error", model);
    }

    public override IEnumerable<DHSitemap> CreateSiteMap()
    {
        var list = new List<DHSitemap>
        {
            new() {
                SType = SiteMap.首页,
                ActionName = "Index",
                ControllerName = "CubeHome"
            }
        };

        return list;
    }

    /// <summary>
    /// 根据父级ID查询商品分类
    /// </summary>
    /// <param name="ParentId"></param>
    /// <returns></returns>
    public IActionResult GetGoodsClassByParentId(long ParentId)
    {
        var list = GoodsClass.FindAllByParentId(ParentId);
        return Json(new { data = list });
    }

    /// <summary>
    /// 查询最新的商品
    /// </summary>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    public IActionResult GetGoodsTop(int page,int limit,string sort = "Id")
    {
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            Sort = sort,
            Desc = true
        };
        var goods = Goods.FindAll(Goods._.GoodsState == 1, pages).Select(e => new 
        {
            Id = e.Id.SafeString(), 
            Name = GoodsLan.FindByGIdAndLId(e.Id,WorkingLanguage.Id)?.LanName ?? e.Name,
            GoodsPrice = e.GoodsPrice * WorkingCurrencies.ExchangeRate,
            GoodsStorage = MerchantMaterial.GetQuantityByWIds(e.MaterialIds),
            GoodsImage = AlbumPic.FindByNameAndSId(GoodsLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.LanGoodsImage ?? e.GoodsImage??"", e.StoreId)?.Cover,
            e.GoodsSalenum,
            e.GoodsBuynum,
            e.EvaluationGoodStar,
        });
        return Json(new { data = goods });
    }

    /// <summary>
    /// 根据一级商品分类ID查询二级商品分类
    /// </summary>
    /// <param name="classId"></param>
    /// <returns></returns>
    public IActionResult GetGoodsClassById(long classId)
    {
        var list = GoodsClass.FindAllByParentId(classId).OrderBy(e => e.Sort).Select(e => new
        {
            Id = e.Id.SafeString(),
            Name = (GoodsClassLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.Name).IsNullOrWhiteSpace() ? e.Name : GoodsClassLan.FindByGIdAndLId(e.Id, WorkingLanguage.Id)?.Name,
            Sum = MerchantMaterial.GetQuantityByClassId(0, e.Id)
        });
        return Json(new { data = list });
    }

    /// <summary>
    /// 加入心愿清单
    /// </summary>
    /// <param name="goodsId"></param>
    /// <param name="sId"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult AddWishlist(long goodsId)
    {
        var res = new DResult();
        var goods = Goods.FindById(goodsId);
        if(goods == null)
        {
            res.msg = GetResource("商品不存在");
            return Json(res);
        }
        var userId = ManageProvider.User?.ID ?? 0;
        if(userId <= 0)
        {
            res.msg = GetResource("请先登录");
            return Json(res);
        }

        var modelWishlist = Wishlist.FindByUIdAndGoddsId(userId, goods.Id);

        if(modelWishlist == null)
        {
            goods.GoodsCollect = goods.GoodsCollect + 1;
            goods.Update();

            Wishlist wish = new()
            {
                BuyerId = userId,
                StoreId = goods.StoreId,
                StoreName = goods.StoreName,
                GoodsId = goods.Id,
                GoodsPrice = goods.GoodsPrice,
                WishlistAddTime = UnixTime.ToTimestamp()
            };
            wish.Insert();
        }

        res.success = true;
        res.msg = GetResource("操作成功");
        return Json(res);
    }

    /// <summary>
    /// 取消心愿清单
    /// </summary>
    /// <param name="goodsId"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult DelWishlist(long goodsId)
    {
        var res = new DResult();
        var userId = ManageProvider.User?.ID ?? 0;
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            res.msg = GetResource("商品不存在");
            return Json(res);
        }
        var wish = Wishlist.FindByUIdAndGoddsId(userId, goods.Id);
        if(wish == null)
        {
            res.msg = GetResource("心愿清单不存在");
            return Json(res);
        }
        goods.GoodsCollect = goods.GoodsCollect - 1;
        goods.Update();
        wish.Delete();
        res.success = true;
        res.msg = GetResource("操作成功");
        return Json(res);
    }

    /// <summary>
    /// 加入购物车
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public IActionResult AddCart(long GoodsId,int GoodsNum,long SkuId)
    {
        var res = new DResult();  
        if (GoodsId <= 0)
        {
            res.msg = GetResource("商品不能为空");
            return Json(res);
        }
        if (GoodsNum <= 0)
        {
            res.msg = GetResource("商品数量必须大于0");
            return Json(res);
        }
        var goods = Goods.FindById(GoodsId);
        if (goods == null)
        {
            res.msg = GetResource("商品不存在");
            return Json(res);
        }
        var skudetail = GoodsSKUDetail.FindById(SkuId);
        Int64 materialId = skudetail?.MaterialId ?? goods.MerchantMaterial;
        var materialModel = MerchantMaterial.FindById(materialId);
        if (materialModel == null)
        {
            res.msg = GetResource("商品物料不存在");
            return Json(res);
        }
        var cart = Cart.FindCartBySIdAndMaterialId(SId, materialId);
        var tieredPrice = MaterialTieredPrice.GetPriceByMaterialIdAndQuantity(materialId, GoodsNum);
      
        if (cart == null)
        {
            cart = new Cart()
            {
                BuyerId = SId,
                StoreId = goods.StoreId,
                StoreName = goods.StoreName,
                GoodsId = goods.Id,
                MaterialId = materialId,
                SKUId = skudetail?.Id ?? 0,
                GoodsPrice = tieredPrice?.Price??materialModel.Price,
                GoodsNum = GoodsNum,
            };
            cart.Insert();
        }
        else
        {
            var werehouseMaterial = WareHouseMaterial.FindAllByMaterialIdLan(materialId, WorkingLanguage.Id).OrderBy(o => o.Quantity).FirstOrDefault();//获取数量最多的仓库
            if (werehouseMaterial==null)
            {
                res.msg = GetResource("库存不足");
                return Json(res);
            }
            if (werehouseMaterial?.Quantity < cart.GoodsNum + GoodsNum)
            {
                res.msg = GetResource("商品加购数量大于库存数量");
                return Json(res);
            }
            cart.GoodsNum +=  GoodsNum;
            cart.Update();
        }
        res.success = true;
        res.msg = GetResource("操作成功");
        res.data = Cart.FindAllByBuyerId(SId).Count;//获取购物车数量
        return Json(res);
    }
    public IActionResult SkuSelector(long goodsId,int num)
    {
        var res = new DResult();
        if (goodsId.IsNull())
        {
            res.msg = GetResource("商品不能为空");
            return Json(res);
        }
        if (num <= 0)
        {
            res.msg = GetResource("商品数量必须大于0");
            return Json(res);
        }
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            res.msg = GetResource("商品不存在");
            return Json(res);
        }
        dynamic viewModel = new ExpandoObject();
        //需要显示的规格/值
        var skulist = GoodsSKUDetail.FindAllByGoodsId(goodsId);
        IDictionary<int, string> dictSpec = new Dictionary<int, string>();
        IDictionary<int, string> dictSpecValue = new Dictionary<int, string>();

        List<GoodsSpecDto> specvalues = new();
        foreach (GoodsSKUDetail item in skulist)
        {
            foreach (var specitem in item.goodsSpecDto)
            {
                if (!dictSpec.ContainsKey(specitem.Id))
                {
                    dictSpec.Add(specitem.Id,GoodsSpecValueLan.FindBySIdAndLId(specitem.Id,WorkingLanguage.Id)?.LanName??specitem.Name);
                }
                if (!dictSpecValue.ContainsKey(specitem.VId))
                {
                    dictSpecValue.Add(specitem.VId,GoodsSpecValueLan.FindBySIdAndLId(specitem.VId, WorkingLanguage.Id)?.LanName ?? specitem.Value);
                    specvalues.Add(specitem);
                }
            }
        }

        viewModel.GoodsId = goods.Id;
        viewModel.Num = num;
        viewModel.Goods = goods;
        viewModel.skuList = skulist;
        viewModel.dictSpec = dictSpec;
        viewModel.specvalues = specvalues;
        return View(viewModel);
    }


    public IActionResult ClickNumber(int Id) 
    {
        if (Id.IsNull()) return Json(new DResult() { success = false, msg = GetResource("广告记录不存在") });
        var adv = Advertising.FindById(Id);
        if (adv.IsNull()) return Json(new DResult() { success = false, msg = GetResource("广告记录不存在") });
        adv.ClickNumber += 1;
        adv.Update();
        return Json(new DResult() { success = true, msg = GetResource("操作成功") });
    }

    /// <summary>
    /// 服务和投诉
    /// </summary>
    /// <returns></returns>
    public IActionResult ServicesAndComplaints()
    {
        return View();
    }
}