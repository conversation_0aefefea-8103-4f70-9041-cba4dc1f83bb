﻿@using B2B2CShop.Dto
@using B2B2CShop.Entity
@inject IWorkContext workContext
@{
    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;

}
<head>
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/public/css/pageCss/checkOrder.css">
    <!-- 综合-script -->
    <script src="~/public/dialog/editAddr.js"></script>
</head>
<body>
    <div class="pageBox">
        <!-- 头部开始 -->
        <div class="orderheader">
            <div class="logo">
                <div>
                    <img src="../../images/icons/hlk.png" class="checkOrderImage">
                </div>
                <div style="width: 1px;background-color: var(--text-color4);padding: 1.5vw 0;margin-left: 10px;"></div>
                <div style="font-size: 1.3vw;color: var(--text-color);padding-left: 20px;">@T("提交订单")</div>
            </div>
            <!-- 步骤条 -->
            <div class="stepBoxContainer">
                <div class="stepBox" style="margin-bottom: 30px;">
                    <div class="item flex" style="max-width: fit-content;">
                        <div class="circle">
                            <span class="number">1</span>
                        </div>
                        <div class="stepText">@T("查看购物车")</div>
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">2</span>
                        </div>
                        @{
                            if (Language?.UniqueSeoCode == "en")
                            {
                                <div class="stepText" style="right:-100px">@T("核对订单信息")</div>
                            }
                            else
                            {
                                <div class="stepText">@T("核对订单信息")</div>
                            }
                        }
                    </div>
                    <div class="item flex">
                        <div class="stepLine"></div>
                        <div class="circle">
                            <span class="number">3</span>
                        </div>
                        @{
                            if (Language?.UniqueSeoCode == "en")
                            {
                                <div class="stepText" style="right:-70px">@T("订单提交成功")</div>
                            }
                            else
                            {
                                <div class="stepText">@T("订单提交成功")</div>
                            }
                        }
                    </div>
                </div>
            </div>

        </div>

        <!-- 主体 -->
        <div class="checkOrderMain">
            <div class="left">
                <!-- 收货信息部分 -->
                <div class="addressBox">
                    <div class="boxTitle">@T("收货人信息")</div>
                    <div class="boxTitle2">
                        @T("收货地址")
                        <span class="addAddressBtn" onclick="openAddressDialog1(0)"> <i class="iconfont icon-tianjia1"></i> @T("新增")</span>
                    </div>
                    <div class="addressList">
                        @foreach (BuyerAddress item in Model.deliveryAddress)
                        {
                            @if (item.IsDefaultDelivery)
                            {
                                <div class="addressItem currentAddr" data-id="@item.Id">
                                    <div class="flex" style="justify-content: left;">
                                        <div>@item.RealName</div>
                                        <div class="phone">@item.Phone</div>
                                        <div class="address">
                                            <div class="textOver">@item.AreaInfo @item.AddressDetail</div>
                                        </div>
                                        <div style="margin-left: auto; color: var(--blue-deep); cursor: pointer;" onclick="openAddressDialog2(0,@item.Id)">
                                            <i class="iconfont icon-bianji"></i>@T("编辑")
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="addressItem" data-id="@item.Id">
                                    <div class="flex" style="justify-content: left;">
                                        <div>@item.RealName</div>
                                        <div class="phone">@item.Phone</div>
                                        <div class="address">
                                            <div class="textOver">@item.AreaInfo @item.AddressDetail</div>
                                        </div>
                                        <div style="margin-left: auto; color: var(--blue-deep); cursor: pointer;" onclick="openAddressDialog2(0,@item.Id)">
                                            <i class="iconfont icon-bianji"></i>@T("编辑")
                                        </div>
                                    </div>
                                </div>
                            }
                        }

                    </div>
                    <div class="boxTitle2" style="border-top: 1px solid var(--line);padding-top: 10px;">
                        @T("发票地址")
                        <span class="addAddressBtn" onclick="openAddressDialog1(1)"> <i class="iconfont icon-tianjia1"></i> @T("新增")</span>
                    </div>
                    <div class="addressList">
                        @foreach (BuyerAddress item in Model.InvoiceAddress)
                        {
                            @if (item.IsDefaultInvoice)
                            {
                                <div class="addressItem currentAddr" data-id="@item.Id">
                                    <div class="flex" style="justify-content: left;">
                                        <div>@item.RealName</div>
                                        <div class="phone">@item.Phone</div>
                                        <div class="address">
                                            <div class="textOver">@item.AreaInfo @item.AddressDetail</div>
                                        </div>
                                        <div style="margin-left: auto; color: var(--blue-deep); cursor: pointer;" onclick="openAddressDialog2(1,@item.Id)">
                                            <i class="iconfont icon-bianji"></i>@T("编辑")
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="addressItem" data-id="@item.Id">
                                    <div class="flex" style="justify-content: left;">
                                        <div>@item.RealName</div>
                                        <div class="phone">@item.Phone</div>
                                        <div class="address">
                                            <div class="textOver">@item.AreaInfo @item.AddressDetail</div>
                                        </div>
                                        <div style="margin-left: auto; color: var(--blue-deep); cursor: pointer;" onclick="openAddressDialog2(1,@item.Id)">
                                            <i class="iconfont icon-bianji"></i>@T("编辑")
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                    </div>

                </div>
                <!-- 快递选择部分 -->
                <div class="expressBox">
                    <div class="boxTitle">@T("快递")</div>
                    <div class="expressContent">
                        <div class="expressType">
                            <div class="typeTitle">
                                @* <input type="radio" name="title"> *@
                                <span>@T("使用自己的配送账户")</span>
                            </div>
                            <div class="typeDesc">
                                @T("国际快递专线特惠型")：<b style="color: var(--text-color);">EXW</b>（@T("运费、关税、海关和税费由买方承担")）
                            </div>
                            <div class="expressOptions recommend-express-options1">
                                @foreach (ExpressDto item in Model.expressList)
                                {
                                    <label class="radioItem">
                                        <input type="radio" name="express" value="@item.Id">
                                        <span>@item.Name</span>
                                    </label>
                                }
                                <label class="radioItem"></label>
                                <input class="input" id="expressInput" placeholder="*@T("请输入配送账户")" style="width:260px">
                            </div>
                        </div>

                        <div class="expressType" style="margin-top: 20px;">
                            <div class="typeTitle">@T("使用推荐的配送账户")</div>
                            <div class="typeDesc">
                                @T("国际贸易术语解释通则")：<b style="color: var(--text-color);">FCA</b>（@T("关税、海关和税费由买方承担"))
                            </div>
                            <div class="expressOptions recommend-express-options2">
                                @foreach (ExpressDto item in Model.expressList)
                                {
                                    <label class="radioItem" data-express-id="@item.Id">
                                        <input type="radio" name="express" value="@item.Id">
                                        <span>@item.Name</span>
                                        <span class="price freight-price" data-express-id="@item.Id">@(symbolLeft+0.00)</span>
                                        <input type="hidden" id="<EMAIL>" value="">
                                    </label>
                                }
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 订单表格 -->
                <div class="tablesBox">
                    <table class="layui-table" lay-skin="line" style="background-color: white;margin: 0;">
                        <colgroup>
                            <col width="40%">
                            <col width="12%">
                            <col width="1%">
                            <col width="10%">
                        </colgroup>
                        <thead>
                            <tr style="background-color: var(--text-color4);">
                                <th class="flex" style="justify-content: left;">
                                    <div>@Model.storeName</div>
                                </th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach(var item in Model.data)
                            {
                                <tr>
                                    <td>
                                        <div class="goodsInfo textOver" style="place-items: center;">

                                            <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                                <img src="@item.GoodsImage" alt="商品图片"
                                                style="width: 100%;margin-top: auto;">
                                            </div>
                                            <div class="goodsInformation"
                                            style="width: 60%;margin-left: 2%;margin-right: auto;">
                                                <div class="tdTitle">
                                                    <span class="textOver">@item.GoodsName</span>
                                                </div>
                                                <div class="tdTitle">
                                                    <p class="gray">@item.Spec</p>
                                                </div>
@*                                                 <div>原厂编号<span class="name textOver">ACDC电源模块</span></div>
                                                <div>制造商: <span class="name textOver">Hi-Link</span> </div>
                                                <div class="textOver">
                                                    制造商编号: <span class="name textOver">727-S40FC008C3B1V000 </span>
                                                </div>
                                                <div class="textOver">
                                                    型号:<span class="name textOver">HLK-PM01</span>
                                                </div>
                                                <div class="textOver">
                                                    客户编号:<span class="name textOver">220V转5V3.3V9V12V15V24V</span>
                                                </div> *@
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>@T("单价")：@<EMAIL></div>
                                    </td>
                                    <td>
                                        <!-- 计数器 -->
                                        <div class="computer">
                                            <div class="iconfont icon-jianhao" onclick="changeNum('@item.GoodsId','-')"></div>
                                            <input type="text" id="@item.GoodsId" value="@item.GoodsNum" class="layui-input myinput" style="height:100%;width:50px;border:none;text-align:center">
                                            <div class="iconfont icon-tianjia1" onclick="changeNum('@item.GoodsId','+')"></div>
                                        </div>
                                    </td>
                                    @if(item == Model.data[0])
                                    {
                                        <td class="tableBtnBox" rowspan="3" colspan="1"
                                            style="border-left: 1px solid rgb(238, 238, 238);">
                                            <div>@T("合计")：<span style="color:red">@<EMAIL></span> </div>
                                        </td>
                                    }
                                </tr>
                            }
                            <!--  -->
@*                             <tr>
                                <td class="hover" style="text-align: center;" colspan="4">
                                    --- 展开所有订单 ---
                                </td>
                            </tr> *@
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="right">
                <!-- 侧边栏 -->
                <div class="aside">
                    <div class="asideItem serverInfoIcon" style="color: black;">
                        @T("结算明细")
                    </div>
                    <div class="asideItem" style="margin-top: 10px;">
                        <div>@T("商品总价")</div>
                        <div id="asideGoodsTotal">@<EMAIL></div>
                    </div>
                    <div class="asideItem">
                        <div>@T("运费价格")</div>
                        <div id="asideFreightPrice">@(symbolLeft + "0.00")</div>
                    </div>
                    <div class="asideItem"
                         style="font-size: 16px;border-bottom: 1px solid var(--line);padding-bottom: 10px ;">
                        <div style="color: black;">@T("合计")</div>
                        <div class="red serverInfoIcon"> <b id="asideTotalPrice">@symbolLeft 0.00</b> </div>
                    </div>
@*                     <div>
                        <div>优惠券码</div>
                        <div class="flex coupon">
                            <input type="text">
                            <button class="button_blue">确定</button>
                        </div>
                    </div> *@
                    <div class="asideItem borderB" style="margin:10px 0 15px 0">
                        <button class="button button_blue" style="width: 95%;margin: 10px auto;border-radius: 45px;" id="submitBtn">
                            @T("提交订单")
                        </button>
                    </div>


                    <!--  -->
                    <div class="serverInfo">
                        <div class="serverTitle" style="font-size: 15px;">@T("服务申明")</div>
                        <div class="serverTitle flex">
                            <div class="iconfont icon-sign serverInfoIcon"
                                 style="margin-left: .2vw;margin-right: .2vw;"></div>
                            <div>@T("快递")</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>@T("支持七天无理由退货")</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>@T("如果快递丢失，支持退货")</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>@T("如果快递损坏，支持退货")</div>
                        </div>
                        <div class="serverItem flex">
                            <div class="iconfont icon-ai210"></div>
                            <div>@T("支持90天内免费换货")</div>
                        </div>

                        <div class="serverTitle"> <i class="iconfont icon-secured serverInfoIcon"></i> @T("安全与隐私")</div>
                        <div class="serverItem" style="margin-top: 3px;">
                            <div>@T("安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。")</div>
                            <div style="margin-top: 3px;">@T("安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。")</div>
                        </div>

                        <div class="serverTitle">
                            <i class="iconfont icon-money-circle serverInfoIcon"></i>
                            @T("支付安全")
                        </div>
                        <div class="serverItem" style="margin-top: 3px;">
                            <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
                                <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
                                <img src="../../images/icons/paypal.png" alt="">
                                <img src="../../images/icons/weixin.png" alt="">
                            </div>
                            <div>@T("与受欢迎的支付合作伙伴合作，您的个人信息是安全的。")</div>
                        </div>
                    </div>
                    <!-- 右侧边栏 -->

                    <script>
                        /** 节流函数 */
                        function throttle(func, delay) {
                            let timer = null;
                            return function (...args) {
                                if (timer === null) {
                                    timer = setTimeout(() => {
                                        func.apply(this, args);
                                        timer = null;
                                    }, delay);
                                }
                            };
                        }
                        function isAtFooter() {
                            const footerRect = document.querySelector('.footer').getBoundingClientRect();
                            const asideRect = document.querySelector('.aside').getBoundingClientRect();
                            return asideRect.bottom > footerRect.top && asideRect.top < footerRect.bottom;
                        }
                        function handleScroll() {
                            if (isAtFooter()) {
                                $('.aside').stop(true, true).fadeOut(300);
                            } else {
                                $('.aside').stop(true, true).fadeIn();
                            }
                        }

                        // 节流处理滚动事件
                        // const throttledScroll = throttle(handleScroll, 200);
                        // window.addEventListener('scroll', throttledScroll);
                    </script>
                </div>
            </div>
        </div>
        <div class="bug"></div>
        <script>
            function next(step = 2) {
                // 01 当前步骤
                const list = $('.item');
                list.each(function (index, item) {
                    if (index < step) {
                        item.classList.add('current')
                    }
                })
                // 02 当前步骤对应的内容
                const stepList = $('[data-step]');
                stepList.each(function (index, item) {
                    if (item.getAttribute('data-step') != step || !item.getAttribute('data-step').includes(step)) {
                        item.style.display = 'none';
                    }
                })

            }
            next(2)
        </script>
    </div>
</body>
<script asp-location="Footer">
    var data = [];
    var WIds = [];
    @for (int i = 0; i < Model.data.Count; i++)
    {
        var goods = Model.data[i];
        <text>
            data.push({
                GoodsId:'@goods.GoodsId',
                GoodsNum:@goods.GoodsNum,
                GoodsPrice:@goods.GoodsPrice,
                MerchantMaterial:'@goods.MerchantMaterial',
                Price:@goods.Price,
                SKUId:'@goods.SKUId',
                TotalWeight:'@goods.TotalWeight',
            });
            </text>;
    }

    $('#submitBtn').click(function(){
        var AId1 = $('.addressList').eq(0).find('.addressItem.currentAddr').data('id');
        if(!AId1){
            layui.layer.msg('@T("请选择收货地址")')
            return;
        }

        var AId2 = $('.addressList').eq(1).find('.addressItem.currentAddr').data('id');
        if(!AId2){
            layui.layer.msg('@T("请选择发票地址")')
            return;
        }

        var $checkedExpress = $('input[name="express"]:checked');

        var EId = $('input[name="express"]:checked').val();
        if(!EId){
            layui.layer.msg('@T("请选择配送方式")')
            return;
        }

        // 判断是否为推荐配送账户
        var isRecommend = $checkedExpress.closest('.recommend-express-options2').length > 0;

        var Account = $('#expressInput').val();

        if(!isRecommend){
            if(!Account){
                layui.layer.msg('@T("请输入配送账户")')
                return;
            }
        }

        var freightText = $('#asideFreightPrice').text();
        // 去掉货币符号和空格，转为数字
        var FrePrice = parseFloat(freightText.replace('@symbolLeft', '').replace(/[^\d.]/g, '').trim()) || 0;

        var SId = '@Model.storeId';
        

        $.post('@Url.Action("SubmitOrder")',{Pamrs:data,AId1,AId2,EId,Account,SId,WIds},function(res){
            console.log(res);
            if(res.success){
                clearShoppingCart();
                // 用 replace 替换历史记录
                window.location.replace('@Url.Action("Payment", "Orders", new { area = "Member" })?Id=' + res.data);
            }else{
                layui.layer.msg(res.msg)
            }
        })
    })

    function clearShoppingCart(){
        var CIds = '@Model.CIds';
        if(CIds){
            $.post('@Url.Action("DelCarts")',{ids:CIds},function(){

            })
        }
    }

    function openAddressDialog1(addresstype){
        layer.open({
                type: 2,
                title: addresstype === 0 ? '@T("新增收货地址")' : '@T("新增发票地址")',
                shadeClose: true,
                shade: 0.8,
                area: ['35%', '80%'],
                content: '@Url.Action("CreateAddress", "Contact", new { Area = "Member" })' + '?addressType=' + addresstype,
                success: function(layero, index) {
                    //将当前窗口索引传递给iframe
                    var iframe = layer.getChildFrame('body', index);
                    if (iframe) {
                        iframe.find('#currentLayerIndex').val(index);
                    }
                },
                end: function() {
                    //弹窗关闭后刷新页面
                    location.reload();
                }
            });
    }

    function openAddressDialog2(addresstype,id){
        layer.open({
                type: 2,
                title: addresstype === 0 ? '@T("编辑收货地址")' : '@T("编辑发票地址")',
                shadeClose: true,
                shade: 0.8,
                area: ['35%', '80%'],
                content: '@Url.Action("EditorAddress", "Contact", new { Area = "Member" })' + '?addressType=' + addresstype+'&id='+id,
                success: function(layero, index) {
                    //将当前窗口索引传递给iframe
                    var iframe = layer.getChildFrame('body', index);
                    if (iframe) {
                        iframe.find('#currentLayerIndex').val(index);
                    }
                },
                end: function() {
                    //弹窗关闭后刷新页面
                    location.reload();
                }
            });
    }

     // 收货地址切换
    $('.addressList').eq(0).on('click', '.addressItem', function () {
        $(this).addClass('currentAddr').siblings().removeClass('currentAddr');
        // 这里可以加上你需要的业务逻辑，比如记录选中的地址ID
        QueryFreightList();
        $('#asideFreightPrice').text('@symbolLeft'+'0.00');
        // 取消所有快递选项的选中
        $('.expressOptions input[type="radio"][name="express"]').prop('checked', false);
        // 清空配送账户输入框
        $('#expressInput').val('');
        updateAsideTotal();
    });

    // 发票地址切换
    $('.addressList').eq(1).on('click', '.addressItem', function () {
        $(this).addClass('currentAddr').siblings().removeClass('currentAddr');
        // 这里可以加上你需要的业务逻辑，比如记录选中的地址ID
    });

    // 只监听“个人配送账户”下的快递选项
    $('.recommend-express-options1').on('change', 'input[type="radio"][name="express"]', function () {
        var expressId = $(this).val();
        // 获取对应的货运账户
        $.ajax({
            url: '@Url.Action("GetFreightAccount", "ShoppingCart")',
            type: 'Get',
            data: { shippingExpressId: expressId },
            success: function(result) {
                if (result.success) {
                    $('#expressInput').val(result.data);
                }
            }
        });
        // 设置右侧运费价格
        $('#asideFreightPrice').text('@symbolLeft'+'0.00');
        updateAsideTotal();
    });

    // 只监听“推荐配送账户”下的快递选项
    $('.recommend-express-options2').on('change', 'input[type="radio"][name="express"]', function () {
        var expressId = $(this).val();
        // 获取对应快递的运费
        var freightText = $('.freight-price[data-express-id="' + expressId + '"]').text();
        // 设置右侧运费价格
        $('#asideFreightPrice').text(freightText || '@symbolLeft 0.00');
        updateAsideTotal();
    });

    // 如果默认选中了个人账户，触发一次快递选项的change事件
    if ($('input[name="title"][value="-1"]').prop('checked')) {
        $('input[name="express"]:checked').trigger('change');
    }

    QueryFreightList();

    function QueryFreightList(){
        // 获取选中的收货地址ID
        var selectedAddressId = $('.addressList').eq(0).find('.addressItem.currentAddr').data('id');
        $.post('@Url.Action("QueryFreightList")',{DId:selectedAddressId,weight:'@Model.weight',Pamrs:data},function(res){
            if(res.success){
                WIds = res.extdata;
                res.data.forEach(function(item){
                    if(item.ShippingCost == 0){
                        $(`.radioItem[data-express-id="${item.LogisticsCompanyId}"]`).hide();
                    }else{
                        $(`.radioItem[data-express-id="${item.LogisticsCompanyId}"]`).show();
                        $(`.freight-price[data-express-id="${item.LogisticsCompanyId}"]`).html('@symbolLeft' + item.ShippingCost);
                    }
                })
            }
        })
    }

updateAsideTotal();

function updateAsideTotal() {
    // 获取商品总价和运费价格，去掉货币符号
    var goodsTotal = parseFloat($('#asideGoodsTotal').text().replace('@symbolLeft', '').trim()) || 0;
    var freight = parseFloat($('#asideFreightPrice').text().replace('@symbolLeft', '').trim()) || 0;
    var total = (goodsTotal + freight).toFixed(2);
    $('#asideTotalPrice').text('@symbolLeft' + total);
}

$(".myinput").on("input", function (e) {
    var id = this.id;
    var val = $(this).val();
    if('@Model.CIds'){
        $.post('@Url.Action("FindByCraIdsAndGoodsIdUpdateGoodsNum")',{CIds:'@Model.CIds',GoodsId:id,Num:val},function(res){
            if(!res.success){
                layui.layer.msg(res.msg);  
            }
            setTimeout(function(){
                window.location.reload();
            },500);
        })  
    }else{
        var GId = '@Model.GId';
        var SkuId = '@Model.SkuId';
        var Num = val;
        $.getJSON('@Url.Action("verifyGoods")',{GId,SkuId,Num},function(res){
            if(res.success){
                window.location.href = '@Url.Action("CheckOrder")?GId='+GId+'&Num='+Num+'&SkuId='+SkuId;
            }else{
                layui.layer.msg(res.msg);  
                setTimeout(function(){
                    window.location.reload();
                },500);
            }
        })
        
    }
});

function changeNum(gid,symbol){
    var num = $("#"+gid).val();
    if(num === ''){
        num = 0;
    }   
    if(symbol === '+'){
        num++;
    }
    if(symbol === '-'){
        if(num <= 1){
            layui.layer.msg('@T("商品数量不能小于1")');
            return;
        }
        num--;
    }

    $("#"+gid).val(num).trigger('input');
}
</script>