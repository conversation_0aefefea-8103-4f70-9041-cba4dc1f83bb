﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using B2B2CShop.Dto;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using Newtonsoft.Json;
using Pek;
using Pek.Timing;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace B2B2CShop.Entity;

public partial class Order : DHEntityBase<Order>
{
    #region 对象操作
    static Order()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(StoreId));

        // 过滤器 UserModule、TimeModule、IPModule

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化Order[订单表]数据……");

    //    var entity = new Order();
    //    entity.OrderSn = "abc";
    //    entity.PaySn = "abc";
    //    entity.StoreId = 0;
    //    entity.StoreName = "abc";
    //    entity.BuyerId = 0;
    //    entity.BuyerName = "abc";
    //    entity.BuyerEmail = "abc";
    //    entity.ChainId = 0;
    //    entity.AddTime = 0;
    //    entity.PaymentCode = "abc";
    //    entity.PaymentTime = 0;
    //    entity.TradeNo = "abc";
    //    entity.FinnshedTime = 0;
    //    entity.GoodsAmount = 0.0;
    //    entity.OrderAmount = 0.0;
    //    entity.RcbAmount = 0.0;
    //    entity.PdAmount = 0.0;
    //    entity.PresellDepositAmount = 0.0;
    //    entity.PresellRcbAmount = 0.0;
    //    entity.PresellPdAmount = 0.0;
    //    entity.PresellTradeNo = "abc";
    //    entity.PresellPaymentCode = "abc";
    //    entity.PresellEndTime = 0;
    //    entity.ShippingFee = 0.0;
    //    entity.EvaluationState = 0;
    //    entity.OrderState = 0;
    //    entity.RefundState = 0;
    //    entity.OrderRefundLockState = 0;
    //    entity.RefundAmount = 0.0;
    //    entity.DeleteState = 0;
    //    entity.DelayTime = 0;
    //    entity.OrderFrom = "abc";
    //    entity.ShippingCode = "abc";
    //    entity.OrderType = 0;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化Order[订单表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>卖家店铺ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Store? Store => Extends.Get(nameof(Store), k => Store.FindById(StoreId));
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public string OrderStateName
    {
        get
        {
            switch (OrderState)
            {
                case 0:
                    return "已取消";
                case 10:
                    return "未付款";
                case 14:
                    return "待付定金";
                case 15:
                    return "代付尾款";
                case 20:
                    return "待发货";
                case 30:
                    return "已发货";
                case 35:
                    return "待自提";
                case 40:
                    return "已收货";
                default:
                    return "未知";
            }
        }
    }

    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public List<OrderGoods> OrderGoodsList => OrderGoods.FindAllByOrderId(Id).ToList();
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="storeId">卖家店铺ID</param>
    /// <param name="buyerId">买家ID</param>
    /// <param name="orderState">订单状态</param>
    /// <param name="buyStratDate">购买开始时间</param>
    /// <param name="buyEndDate">购买结束时间</param>
    /// <param name="deleteState">删除状态</param>
    /// <param name="deleteState">评价状态</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<Order> Search(Int64 storeId, Int32 buyerId, Int32 orderState,DateTime buyStratDate, DateTime buyEndDate,int deleteState,Int32 evaluationState, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (storeId >= 0) exp &= _.StoreId == storeId;
        if (buyerId >= 0) exp &= _.BuyerId == buyerId;
        if (orderState >= 0) exp &= _.OrderState == orderState;
        if (deleteState >= 0) exp &= _.DeleteState == deleteState;
        if (evaluationState >= 0) exp &= _.EvaluationState == evaluationState;
        if(buyStratDate != DateTime.MinValue)
        {
            var timespan1 = UnixTime.ToTimestamp(buyStratDate);
            exp &= _.AddTime >= timespan1;
        }
        if (buyEndDate != DateTime.MinValue)
        {
            var timespan2 = UnixTime.ToTimestamp(buyEndDate);
            exp &= _.AddTime <= timespan2;
            exp &= _.DeleteState <= timespan2;
        }
        if (!key.IsNullOrEmpty()) exp &= _.OrderSn.Contains(key);
        //exp &= _.RefundState == 0;
        return FindAll(exp, page);
    }
    public static IList<Order> Search(Int64 storeId, string buyName, string orderStates,string orderSn, DateTime buyStratDate, DateTime buyEndDate, PageParameter page)
    {
        var exp = new WhereExpression();

        if (storeId > 0) exp &= _.StoreId == storeId;
        if (!buyName.IsNullOrEmpty()) exp &= _.BuyerName.Contains(buyName);
        if (!orderStates.IsNullOrEmpty()) exp &= _.OrderState.In(orderStates);
        if (!orderSn.IsNullOrEmpty()) exp &= _.OrderSn.StartsWith(orderSn);
        if (buyStratDate != DateTime.MinValue)
        {
            buyStratDate = Convert.ToDateTime(buyStratDate.ToString("yyyy-MM-dd 00:00:00"));
            var timespan1 = UnixTime.ToTimestamp(buyStratDate);
            exp &= _.AddTime >= timespan1;
        }
        if (buyEndDate != DateTime.MinValue)
        {
            buyEndDate = Convert.ToDateTime(buyEndDate.ToString("yyyy-MM-dd 23:59:59"));
            var timespan2 = UnixTime.ToTimestamp(buyEndDate);
            exp &= _.AddTime <= timespan2;
            exp &= _.DeleteState <= timespan2;
        }
        return FindAll(exp, page);
    }

    /// <summary>
    /// 根据买家ID查找订单各状态数量
    /// </summary>
    /// <param name="BuyerId"></param>
    /// <returns></returns>
    public static OrderStateDto FindAllOrderStateNum(Int64 BuyerId)
    {
        var list = FindAll(_.BuyerId == BuyerId);
        var state1 = list.Count(e => e.OrderState == 10);//待支付
        var state2 = list.Count(e => e.OrderState == 20);//待发货
        var state3 = list.Count(e => e.OrderState == 30);//配送中 已发货
        var state4 = list.Count(e => e.OrderState == 35);//待签收
        var state5 = list.Count(e => e.OrderState == 40);//已收货
        var all = list.Count();//所有
        return new OrderStateDto { State1 = state1, State2 = state2, State3 = state3, State4 = state4, State5 = state5,All=all };
    }
    // Select Count(Id) as Id,Category From DH_Order Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<Order> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IOrder ToModel()
    {
        var model = new Order();
        model.Copy(this);

        return model;
    }
    /// <summary>
    /// 订单统计
    /// </summary>
    /// <param name="storeId"></param>
    /// <param name="startTime"></param>
    /// <param name="endTime"></param>
    /// <returns></returns>
    public static decimal[] OrderStatistics(long storeId,DateTime startTime,DateTime endTime)
    {
        if (storeId.IsNull()) return [];
        if (startTime.IsNull()) return [];
        if (endTime.IsNull()) return [];
        
        var exp = new WhereExpression();
        exp &= _.StoreId == storeId;
        var timespan1 = UnixTime.ToTimestamp(startTime);
        exp &= _.PaymentTime >= timespan1;
        var timespan2 = UnixTime.ToTimestamp(endTime);
        exp &= _.PaymentTime <= timespan2;
        var orders = FindAll(exp);
        var orderCount = orders.Count;
        var totalAmount = orders.Sum(o => o.OrderAmount);
        
        return [orderCount, totalAmount];
    }
    public static List<OrderGoodsDto> GetSalesRanking(long storeId, DateTime startTime, DateTime endTime)
    {
        if (storeId.IsNull()) return [];
        if (startTime.IsNull()) return [];
        if (endTime.IsNull()) return [];

        var exp = new WhereExpression();
        exp &= _.StoreId == storeId;
        var timespan1 = UnixTime.ToTimestamp(startTime);
        exp &= _.PaymentTime >= timespan1;
        var timespan2 = UnixTime.ToTimestamp(endTime);
        exp &= _.PaymentTime <= timespan2;
        var orders = FindAll(exp);
        var ordersIds = orders.Select(e => e.Id).ToList().Join(",");
        var ordergoods = OrderGoods.FindAllByOrderIds(ordersIds);
        var top5Materials = ordergoods
            .GroupBy(og => og.MaterialId)
            .Select(g => new OrderGoodsDto
            {
                MaterialId = g.Key,
                MaterialName = MerchantMaterial.FindById(g.Key)?.Name,
                GoodsNum = g.Sum(og => og.GoodsNum)
            })
            .OrderByDescending(dto => dto.GoodsNum)
            .Take(5)
            .ToList();

        return top5Materials;

    }
    #endregion
}