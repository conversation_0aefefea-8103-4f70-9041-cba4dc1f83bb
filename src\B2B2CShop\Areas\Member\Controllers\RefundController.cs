﻿using B2B2CShop.Common;
using B2B2CShop.Dto;
using B2B2CShop.Entity;
using DH.Entity;
using DH.RateLimter;
using MailKit.Search;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;
using Newtonsoft.Json.Linq;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.NCube;
using Pek.NCubeUI.Common;
using Pek.Timing;
using Pek.Webs;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Security.Cryptography.X509Certificates;
using XCode;
using XCode.Membership;
using static SKIT.FlurlHttpClient.Wechat.TenpayV3.Models.CreateNewTaxControlFapiaoApplicationRequest.Types.Fapiao.Types;

namespace B2B2CShop.Areas.Member.Controllers;

/// <summary>
/// 会员退货退款控制器
/// </summary>
[MemberArea]
[MemberAuthorize]
public class RefundController : PekBaseControllerX {

    /// <summary>
    /// 
    /// </summary>
    /// <param name="searchKey">关键字</param>
    /// <param name="start">关键字</param>
    /// <param name="end">关键字</param>
    /// <param name="refundState">退货状态</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    public IActionResult Index(string searchKey, DateTime buyStartDate, DateTime buyEndDate, int refundState = -1, int page = 1,int limit = 5)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };
        var userId = ManageProvider.User?.ID ?? 0;

        var list = RefundReturn.Search(-1,-1,userId,-1,searchKey, refundState, buyStartDate, buyEndDate, pages).Select(e => new RefundReturnDto
        {
            Id = e.Id.SafeString(),
            OrderSn = e.OrderSn,
            StoreId = e.StoreId.SafeString(),
            StoreName =GetResource(e.StoreName),
            RefundType = e.RefundType,
            ReturnType = e.ReturnType,
            SymbolLeft = e.SymbolLeft,
            RefundAmount = e.RefundAmount,
            RefundreturnAdminState = e.RefundreturnAdminState,
            RefundreturnSellerState = e.RefundreturnSellerState,
            RefundreturnGoodsState = e.RefundreturnGoodsState,
            AddTime = UnixTime.ToDateTime(e.RefundreturnAddTime),
            OrderGoods = OrderGoods.FindAllByOrderIdLan(e.OrderId, WorkingLanguage.Id),
        });

        viewModel.list = list;
        viewModel.searchKey = searchKey;
        viewModel.buyStartDate = buyStartDate;
        viewModel.buyEndDate = buyEndDate;
        viewModel.refundState = refundState;
        viewModel.page = page;
        viewModel.limit = limit;
        viewModel.total = pages.TotalCount;
        //获取商品相关推荐
        ViewBag.Randomlist = Goods.FindAllByRandomLan(6, WorkingLanguage.Id, WorkingCurrencies.ExchangeRate);
        return View(viewModel);
    }

    /// <summary>
    /// 添加退货退款
    /// </summary>
    /// <returns></returns>
    public async Task<IActionResult> Add(long OrderId,string Reason,int Way,int State,string ImageUrl)
    {
        var result = new DResult();

        var order = B2B2CShop.Entity.Order.FindById(OrderId);
        if(order == null)
        {
            result.msg = GetResource("订单不存在");
            return Json(result);
        }
        if (order.RefundState == 2)
        {
            result.msg = GetResource("该订单已退款");
            return Json(result);
        }
        JObject censorRes = ContentReviewHelp.textCensor.TextCensorUserDefined(Reason);
        if (censorRes["conclusionType"].ToInt() == 2)//内容不合规
        {
            result.success = false;
            result.msg = GetResource("内容不合规");
            return Json(result);
        }
        //if (ImageUrl.IsNotNullAndWhiteSpace())
        //{
        //    if (!System.IO.File.Exists("https://localhost:9091/"+ImageUrl))
        //    {
        //        result.success = false;
        //        result.msg = GetResource("图片文件未找到，请重新上传！");
        //        return Json(result);
        //    }
        //    XTrace.WriteLine(ImageUrl);
        //    byte[] imageBytes = System.IO.File.ReadAllBytes(ImageUrl);
        //    censorRes = ContentReviewHelp.imageCensor.UserDefined(imageBytes);
        //    var filePath = Path.Combine(Directory.GetCurrentDirectory(), ImageUrl);
        //    if (censorRes["conclusionType"].ToInt() == 2)//图片不合规
        //    {
        //        result.success = false;
        //        result.msg = GetResource("图片不合规");
        //        return Json(result);
        //    }
        //}

        RefundReturn refundReturn = new();
        refundReturn.OrderId = order.Id;
        refundReturn.OrderSn = order.OrderSn;
        refundReturn.RefundSn = Guid.NewGuid().SafeString();
        refundReturn.StoreId = order.StoreId;
        refundReturn.StoreName = order.StoreName;
        refundReturn.BuyerId = order.BuyerId;
        refundReturn.UName = order.BuyerName;
        refundReturn.GoodsId = 0;
        refundReturn.OrderGoodsId = 0;
        refundReturn.CurrencyCode = order.CurrencyCode;
        refundReturn.CurrencyRate = order.CurrencyRate;
        refundReturn.SymbolLeft = order.SymbolLeft;
        refundReturn.SymbolRight = order.SymbolRight;
        refundReturn.RefundAmount = order.CurOrderAmount;
        if (Way == 1)
        {
            refundReturn.RefundType = 2;
            refundReturn.ReturnType = 2;
        }
        else if (Way == 2)
        {
            refundReturn.RefundType = 1;
            refundReturn.ReturnType = 1;
        }
        else if (Way == 3)
        {
            refundReturn.RefundType = 2;
            refundReturn.ReturnType = 2;
        }
        refundReturn.ReturnState = State;

        if (order.OrderState == 20)
        {
            refundReturn.RefundreturnSellerState = 4;
            refundReturn.RefundreturnSellerTime = UnixTime.ToTimestamp();
            refundReturn.RefundreturnAdminState = 3;
            refundReturn.RefundreturnAdminTime = UnixTime.ToTimestamp();
            var orderPay = OrderPay.Find(OrderPay._.PaySn == order.PaySn);
            if (orderPay == null)
            {
                result.msg = GetResource("支付订单不存在");
                return Json(result);
            }
            if (orderPay.PayOkSn.IsNullOrWhiteSpace())
            {
                result.msg = GetResource("订单未支付成功");
                return Json(result);
            }
            if(order.PaymentCode == "PayPal")
            {
                var response = await PayPalHelper.OrderRefund(orderPay.PayOkSn);
                XTrace.WriteLine($"PayPal退款：{response.ToJson()}");
            }
            else if(order.PaymentCode == "ZhiFuBao")
            {

                var rate = Currencies.FindByCode("CNY")?.ExchangeRate ?? 1;

                var amount = order.OrderAmount * rate;

                var response = AliPayHelper.AliTradeRefund(orderPay.PayOkSn, Reason, amount.ToString("N2"));
                XTrace.WriteLine($"支付宝退款：{response.ToJson()}");
                if(response.Code != "10000")
                {
                    result.msg = GetResource("退款失败");
                    return Json(result);
                }
            }
            else if (order.PaymentCode == "WeiXin")
            {

                var rate = Currencies.FindByCode("CNY")?.ExchangeRate ?? 1;

                var amount = order.OrderAmount * rate * 100;

                //var response = WxPayHelper.RefundPay(orderPay.PayOkSn, amount.ToString("N2"));
                var response = WxPayHelper.RefundPay(orderPay.PayOkSn, amount.ToInt()).Result;

                XTrace.WriteLine($"微信退款：{response.ToJson()}");
                if (response.Status  != "SUCCESS" && response.Status != "PROCESSING")
                {
                    result.msg = GetResource("退款失败");
                    return Json(result);
                }
            }
            MerchantMaterial.UpdateTemporaryQuantity(order.Id);//去除暂扣库存
            TemporaryInventory.DeleteByOrderId(order.Id);//删除暂扣库存

        }
        else
        {
            refundReturn.RefundreturnSellerState = 1;
            refundReturn.RefundreturnAdminState = 1;
        }
        refundReturn.RefundreturnAddTime = UnixTime.ToTimestamp();
        refundReturn.RefundreturnBuyerMessage = Reason;
        refundReturn.PicInfo = ImageUrl;

        refundReturn.Insert();

        order.RefundState = 2;
        order.Update();

        result.success = true;
        result.msg = GetResource("操作成功");
        return Json(result);
    }

    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg(Int32 Id, IFormFile file)
    {
        var bytes = file.OpenReadStream().ReadBytes(file.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        // 百度云图片内容审核
        var censorRes = ContentReviewHelp.imageCensor.UserDefined(bytes);
        if (censorRes != null && censorRes["conclusionType"]?.ToInt() == 2)
        {
            return Json(new { file_id = 0, msg = GetResource("图片内容不合规") });
        }
        var fileModel = new UploadInfo();
        fileModel.FileSize = file.Length;
        fileModel.FileType = 1;
        fileModel.ItemId = Id;

        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
        var filepath = $"Refund/{filename}";
        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);

        XTrace.WriteLine($"获取保存的路径:{saveFileName}");

        filepath = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");

        saveFileName.EnsureDirectory();
        file.SaveAs(saveFileName);

        fileModel.FileName = filename;
        fileModel.FileUrl = filepath;
        fileModel.Insert();

        return Json(new { file_id = fileModel.Id, file_name = filename, file_path = filepath });
    }

    /// <summary>
    /// 退货退款详情
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    public IActionResult Detail(Int64 Id)
    {
        var model = RefundReturn.FindById(Id);
        if (model == null) return Json(new DResult { success = false, msg = GetResource("退款记录不存在") });

        var order = B2B2CShop.Entity.Order.FindById(model.OrderId);
        
        ViewBag.Order = order;

        var orderGoods = OrderGoods.FindAllByOrderIdLan(model.OrderId, WorkingLanguage.Id);

        ViewBag.Goods = orderGoods[0];

        return View(model);
    }

    /// <summary>
    /// 订单PayPal退款
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> OrderPayPalRefund(Int64 orderId,String remark)
    {
        var result = new DResult();

        var order = B2B2CShop.Entity.Order.FindById(orderId);
        if(order == null)
        {
            result.msg = GetResource("订单不存在");
            return Json(result);
        }
        var orderPay = OrderPay.Find(OrderPay._.PaySn == order.PaySn);
        if (orderPay == null)
        {
            result.msg = GetResource("支付订单不存在");
            return Json(result);
        }
        if (orderPay.PayOkSn.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("订单未支付成功");
            return Json(result);
        }
        var refund = RefundReturn.FindByOrderId(orderId);
        if(refund == null)
        {
            result.msg = GetResource("退款订单不存在");
            return Json(result);
        }
        var data = await PayPalHelper.OrderRefund(orderPay.PayOkSn);
        if(data.Data.Status == RefundStatus.Completed)
        {

            refund.RefundreturnSellerState = 2;
            refund.RefundreturnSellerTime = UnixTime.ToTimestamp();
            refund.RefundreturnSellerMessage = remark;
            refund.RefundreturnAdminState = 3;
            refund.Update();

            result.success = true;
            result.msg = GetResource("操作成功");

        }
        else
        {
            result.msg = GetResource("操作失败");
        }
        return Json(result);
    }
    public IActionResult Return(long id)
    {
        var refund = RefundReturn.FindById(id);
        if (refund == null)
        {
            return Content(GetResource("退款记录不存在"));
        }

        if (refund.RefundreturnGoodsState != 1)
        {
            return Content(GetResource("当前状态不允许提交退货信息"));
        }

        // 如果是弹窗请求，使用不同的布局
        if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
        {
            return PartialView(refund);
        }

        return View(refund);
    }
    [HttpPost]
    [DisplayName("提交退货信息")]
    public IActionResult SubmitReturn(long refundId, string expressName, string expressCode)
    {
        var result = new DResult();
        var refund = RefundReturn.FindById(refundId);
        if (refund == null)
        {
            result.success = false;
            result.msg = GetResource("退货订单不存在");
            return Json(result);
        }
        if (refund.RefundreturnGoodsState != 1)
        {
            result.success = false;
            result.msg = GetResource("当前状态不允许提交退货信息");
            return Json(result);
        }
        refund.RefundreturnGoodsState = 2;
        refund.RefundreturnShipTime = UnixTime.ToTimestamp(DateTime.Now);
        refund.ExpressName = expressName;
        refund.ExpressCode = expressCode;
        refund.Update();
        RefundReturn.Meta.Cache.Clear("", true);
        result.success = true;
        result.msg = GetResource("已提交");
        return Json(result);
    }
}
