@model B2B2CShop.Entity.Goods
@using B2B2CShop.Dto
@using B2B2CShop.Entity
@using Pek.Timing
@inject IWorkContext workContext
@inject IManageProvider _provider
@{
    //Seo
    PekHtml.AppendTitleParts(Model.SeoTitle + DHSetting.Current.PageTitleSeparator + "Hi_Link");
    PekHtml.AppendMetaKeywordParts(Model?.SeoKeys??"");
    PekHtml.AppendMetaDescriptionParts(Model?.SeoDescription??"");

    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/productDetails.css");

    var symbolLeft = workContext.WorkingCurrency.SymbolLeft;
    var user = _provider.TryLogin(Context);
    var marketPrice = Model.GoodsMarketPrice == 0 ? Model.GoodsPrice : Model.GoodsMarketPrice;
    var goodsPrice = Model.GoodsPrice;
    var discount = marketPrice == 0 ? 0 : ((marketPrice - goodsPrice) / marketPrice * 100);
    var goodskuDetails = GoodsSKUDetail.FindAllByGoodsId(Model.Id);
    //需要显示的规格/值
    IDictionary<int, string> dictSpec = new Dictionary<int, string>();
    IDictionary<int, string> dictSpecValue = new Dictionary<int, string>();
    List<GoodsSpecDto> specvalues = new();
    foreach (var item in goodskuDetails??[])
    {
        foreach (var specitem in item.goodsSpecDto)
        {
            if (!dictSpec.ContainsKey(specitem.Id))
            {
                dictSpec.Add(specitem.Id, specitem.Name);
            }
            if (!dictSpecValue.ContainsKey(specitem.VId))
            {
                dictSpecValue.Add(specitem.VId, specitem.Value);
                specvalues.Add(specitem);
            }
        }
    }
}
<body>

    <!-- 头部结束 -->
    <div class="_main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div class="textSelect">
                @Model.Name
            </div>
        </div>

        <!-- 正片开始 -->
        <div class="productBox">
            <div class="left flex">
                <div class="left-image">
                    @foreach (var item in ViewBag.Images)
                    {
                        <div class="thumbnail-item" onclick="switchMainImage(this)"><img src="@item" alt="@T("产品图片")"></div>
                    }
                </div>
                <div class="main-image">
                    <div style="position: relative;">
                        <img id="mainImage" src="@Model.GoodsImage" alt="">
                        @if (!string.IsNullOrEmpty(Model.GoodsVideoName))
                        {
                            <div class="video-overlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: #000;">
                                <div class="plyr__video-embed" id="player">
                                    <iframe id="vimeoPlayer"
                                    src="@Model.GoodsVideoName"
                                    allowfullscreen
                                    allowtransparency
                                    allow="autoplay">
                                    </iframe>
                                </div>
                            </div>
                            <i class="iconfont video-icon" onclick="toggleVideo()"
                            style="position: absolute; left: 10px; top: 10px; font-size: 12px; color: white; cursor: pointer; background: rgba(44, 121, 232, 0.5); padding: 2px 4px; border-radius: 15px; display: flex; align-items: center; gap: 3px;">
                                &#xe663; <a>@T("视频")</a>
                            </i>
                        }
                    </div>
                </div>
            </div>
            <div class="right">
                <div class="productTitle">@Model.Name</div>
                @*  <div class="flex"> <div>@T("原厂编号"):    </div>     <div class="textOver" style="margin-left: auto; width: 60%;">727-S40FC008C3B1V000  </div>      </div> *@
                <div class="flex">
                    <div>@T("制造商"):      </div>
                    <a class="textSelect pointer" style="margin-left: 10px; width: 60%;" href="/Supplier/ProviderDetail?sid=@Model.StoreId">
                        @ViewBag.CompanyName
                    </a>
                </div>
                @*   <div class="flex"> <div>@T("制造商编号"):  </div>     <div class="textOver" style="margin-left: auto; width: 60%;">727-S40FC008C3B1V000  </div>      </div> *@
                @* <div class="flex"> <div>@T("型号"):       </div>      <div class="textOver" style="margin-left: auto; width: 60%;">             </div>      </div> *@
                @*      <div class="flex"> <div>@T("封装"):       </div>      <div class="textOver" style="margin-left: auto; width: 60%;">插件                 </div>       </div>
                <div class="flex"> <div>@T("包装方式"):   </div>      <div class="textOver" style="margin-left: auto; width: 60%;">盒装                  </div>      </div> *@

                <div class="starBox">
                    <div id="ID-rate-demo"></div> @Model.EvaluationCount @T("个评价") | @Model.GoodsBuynum@T("人购买")
                </div>
                <script>
                    layui.use(function(){
                    var rate = layui.rate;
                    // 渲染
                    rate.render({
                    elem: '#ID-rate-demo',
                    half: true,
                    readonly:true,
                    value:'@Model.EvaluationGoodStar'
                    });
                    });
                </script>

                @{
                    if (goodskuDetails.Count>0)
                    {
                        <input type="hidden" name="skuMarketPrice" id="skuMarketPrice" />
                        <input type="hidden" name="skuPrice" id="skuPrice" />
                        @foreach (var item in dictSpec)
                        {
                            <div class="configBox">
                                <div style="width: 100%;" data-spec-id="@item.Key">
                                    @(GoodsSpecValueLan.FindBySIdAndLId(item.Key, workContext.WorkingLanguage.Id)?.LanName ?? item.Value)
                                </div>
                                @{
                                    var firstValue = true;
                                }
                                @foreach (var itemvalue in specvalues)
                                {
                                    if (itemvalue.Id != item.Key) continue;

                                    <div onclick="selectDiv(this)"
                                    data-spec-id="@item.Key"
                                    data-value-id="@itemvalue.VId"
                                    data-select="@(firstValue ? "true" : "")">
                                        @(GoodsSpecValueLan.FindBySIdAndLId(itemvalue.VId, workContext.WorkingLanguage.Id)?.LanName ?? itemvalue.Value)
                                    </div>
                                    firstValue = false;
                                }
                            </div>
                        }
                    }
                }

                <div class="booksBtnBox">
                    <button class="button" onclick="download('@Model.Specification','')"><i class="iconfont icon-pdf"></i>  @T("规格书")</button>
                    @*<button class="button">  <i class="iconfont icon-tubiao_daochuCAD"></i>  @T("CAD工具")</button>*@
                </div>
            </div>
        </div>
        <!-- 产品推荐 -->
        <div class="productComment">
            <div class="title">
                @T("产品推荐")
            </div>
            <div class="mainBox2_container">
                @foreach (GoodsDto item in ViewBag.Randomlist)
                {
                    <div class="mainBox2_content">
                        <div>
                            <a href="@Url.Action("Index", "Goods", new { goodsId = item.Id })">
                                <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" class="lazy-load" alt="@T("商品图片")">
                            </a>
                        </div>
                        <div class="mainBox2_content_desc">@item.Name</div>
                        <div class="gray">
                            @item.GoodsBuynum@T("人购买") <i class="iconfont icon-star">@item.EvaluationGoodStar</i>
                        </div>
                        <div class="mainBox2_content_price">@<EMAIL></div>
                        <!-- 加入购物车 -->
                        @if (item.GoodsSKUDetail.Count() <= 0)
                        {
                            <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.Id','','',true)"></div>
                        }
                        else if (item.GoodsStorage <= 0)
                        {
                            <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.Id','','@T("库存不足")',false)"></div>
                        }
                        else
                        {
                            <div class="iconfont icon-icon1" onclick="addCarSKU(1,'@item.Id','@T("选择商品属性")','',false)"></div>
                        }
                    </div>
                }
            </div>
        </div>

    </div>
    <!-- 商品参数 / 用户评价 -->


    <div class="optionsBox2">
        <div class="titleBox1 flex">
            <div data-select="false" onclick="titleClick(this,1)" class="titleSelect" style="margin-left: 1vw;">@T("商品参数")</div>
            <div data-select="false" onclick="titleClick(this,2)" style="margin-left: 20px;">@T("用户评价")</div>
        </div>
    </div>

    <!-- 评论头 -->
    <div class="commentHeader" style="display: none;">
        <div class="titleBox">
            <div>@T("用户评价")</div>
        </div>
        <div class="optionsBox flex">
            <div data-select="false" onclick="selectDiv(this,'titleSelect')" class="titleSelect" style="margin-left: 0px;">@T("全部")</div>
            @*<div data-select="false" onclick="selectDiv(this,'titleSelect')">@T("有图")</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">追评</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">好评</div>
            <div data-select="false" onclick="selectDiv(this,'titleSelect')">差评</div>
            <div style="margin-left: auto;">默认排序 <i class="iconfont icon-xiangxia"></i> </div>*@
        </div>
    </div>
    <!-- 评论区 -->
    <div class="commentBox" style="display: none; height:1000px;">
        <div class="commentContainer">
            <div class="commentContent">
                <!-- 每一条评论 -->
                @foreach (var item in ViewBag.Evaluatelist)
                {
                    <div class="comment">
                        <div class="commentUserInfo flex">
                            <div class="avatar" style="width: 5%;">
                                <img src="../../images/icons/zhanghao.png" alt="">
                            </div>
                            <div class="userInfo">
                                @if (item.IsAnonymous)
                                {
                                    <div class="userName">@T("匿名评价")</div>
                                }
                                else
                                {
                                    <div class="userName">@item.FromMemberName</div>
                                }
                                @{
                                    var time = UnixTime.ToDateTime(item.AddTime);
                                }
                                <div>@time · @item.SpecValue【@item.GoodsName】</div>
                            </div>
                            @*<div class="iconfont icon-icon" style="margin-left: auto;">有用(6)</div>*@
                        </div>
                        <div class="commentText">
                            @item.Content
                            <div class="imageBox">
                                @if (!string.IsNullOrEmpty(item.Image1))
                                {        
                                    <img src="@item.Image1" alt="">
                                }
                                @if (!string.IsNullOrEmpty(item.Image2))
                                {
                                    <img src="@item.Image2" alt="">
                                }
                                @if (!string.IsNullOrEmpty(item.Image3))
                                {
                                    <img src="@item.Image3" alt="">
                                }
                                @if (!string.IsNullOrEmpty(item.Image4))
                                {
                                    <img src="@item.Image4" alt="">
                                }
                                @if (!string.IsNullOrEmpty(item.Image5))
                                {
                                    <img src="@item.Image5" alt="">
                                }
                                @if (!string.IsNullOrEmpty(item.Image6))
                                {
                                    <img src="@item.Image6" alt="">
                                }
                            </div>
                        </div>
                    </div>
                }
                <!--  -->
                @*<div class="allComments">
                    <span>  @T("查看全部") <i class="iconfont icon-a-zoomline"></i> </span>
                </div>*@
            </div>
            <!--  -->
        </div>
    </div>
    <!-- & 商品参数 -->

    <div class="goodsDataTitle0 goodsDataTitle1">
        @T("商品参数")
        @* <button class="button button_blue" style="margin-left: auto;">@T("查看类似产品")</button> *@
    </div>
    <div class="goodsDataTable">
        <table style="width: 100%;text-align: center;border: 1px solid #BABABA;overflow: hidden;" name="tableProgram">
            <colgroup>
                <col width="100">
            </colgroup>
            <thead>
                <tr style="background-color: var(--text-color4);">
                    <th style="padding: 1vw;">
                        <span class="layui-form">
                            <input type="checkbox" name="selectAll">
                        </span>
                    </th>
                    <th>@T("类型")</th>
                    <th>@T("描述")</th>
                </tr>
            </thead>

            <tbody>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="checkClass" id="checkClass" disabled checked>
                        </span>
                    </td>
                    <td>@("产品类型")</td>
                    <td>@ViewBag.GoodsClassName</td>
                </tr>
                <tr>
                    <td>
                        <span class="layui-form">
                            <input type="checkbox" name="checkCompanyName">
                        </span>
                    </td>
                    <td>@T("制造商")</td>
                    <td>@ViewBag.CompanyName</td>
                </tr>
                @if (ViewBag.GoodsAttributes != null && ViewBag.GoodsAttributes.Count > 0)
                {
                    foreach (GoodsAttributesDto item in ViewBag.GoodsAttributes)
                    {
                        <tr>
                            <td>
                                <span class="layui-form">
                                    <input type="checkbox" name="@item.Field" value="@item.Value">
                                </span>
                            </td>
                            <td>@item.Attributes</td>
                            <td>@item.Value</td>
                        </tr>
                    }
                }

            </tbody>
        </table>
        <div class="likes flex"><div style="margin-left: auto;">
            <a id="Similarhref"> @T("显示相似产品") ( <span name="SimilarNum" id="SimilarNum"> 0 </span> ) </a>
            
        </div></div>
    </div>
    @if (!Model.ProductManual.IsNullOrEmpty())
    {
        <div class="goodsDataTitle1 goodsDataTitle1">
        @T("产品手册PDF")
        </div>
        <div class="profilePDF">
            <iframe id="pdfFrame" allowtransparency=“true” src="@Model.ProductManual"></iframe>
        </div>
    }
  
    <div class="goodsDataTitle2 goodsDataTitle1" style="padding: 1vw;margin-top: 2vw;">@T("购物指南")</div>

    <div class="shoppingGuide goodsDataTitle3">
        <img src="" alt="">
    </div>
    <div class="bug"></div>

    <aside class="orderAside" style="display: block;">
        @*<div class="price">
            <div>
                <b id="currentPrice" style="font-size: 1.7vw;">@<EMAIL></b>
                <span id="originalPrice" class="gray" style="text-decoration: line-through;">
                    @(marketPrice)
                </span>
                <span id="discountPercentage" class="discount">
                    @discount.ToString("F2")%
                </span>
            </div>
        </div>*@

        <div class="asideTextTitle">@T("阶梯价格")</div>
        <div class="tableBox" style="">
            <table style="height: 8vw;width: 100%;text-align: center;">
                <thead>
                    <tr style="background-color: var(--text-color4);">
                        <th style="padding: 0.2vw;">@T("梯度")</th>
                        <th style="padding: 0.2vw;">@T("原价")</th>
                        <th style="padding: 0.2vw;">@T("售价")</th>
                        @* <th style="padding: 0.2vw;">@T("折合1盒")</th> *@
                    </tr>
                </thead>
                <tbody>
                    @if (ViewBag.TieredPrices != null && ViewBag.TieredPrices.Count > 0)
                    {
                        var lastIndex = ViewBag.TieredPrices != null ? ViewBag.TieredPrices.Count - 1 : -1;
                        for (int i = 0; i < ViewBag.TieredPrices.Count; i++)
                        {
                            var tier = ViewBag.TieredPrices[i];
                            <tr>
                                <td>@tier.MinQuantity+</td>
                                <td>@<EMAIL>("F2")</td>
                                <td>@<EMAIL>("F2")</td>
                                @* <td>@symbolLeft@((tier.Price / (tier.MinQuantity > 0 ? tier.MinQuantity : 1)).ToString("F2"))</td> *@
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td>1+</td>
                            <td>@<EMAIL>("F2")</td>
                            <td>@<EMAIL>("F2")</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="asideTextTitle">@T("库存总量")</div>
        <div class="tableBox">
            <table id = "wareHouseTable" style="height: 2.5vw;width: 100%;text-align: center;">
                <thead>
                    <tr style="background-color: var(--text-color4);">
                    @if (ViewBag.WareHouses.Count == 0)
                    {
                        <th style="padding: 0.2vw;">@T("暂无库存")</th>
                    }
                    else
                    {
                        foreach (WareHouseMaterialDto item in ViewBag.WareHouses)
                        {
                            <th style="padding: 0.2vw;">@item.WareHouseName</th>
                        }
                    }

                    </tr>
                </thead>
                <tbody>
                    <tr>
                        @foreach (WareHouseMaterialDto item in ViewBag.WareHouses)
                        {
                            <th style="padding: 0.2vw;">@item.Quantity</th>
                        }
                    </tr>
                </tbody>
            </table>
        </div>
        <div></div>
        <div class="asideTextTitle" style="padding-top: 0vw;">@T("购买数量")</div>
        <!-- 计数器 -->
        <div class="computer">
            <div class="iconfont icon-jianhao" onclick="compute2(0,1,1)"></div>
            <input type="text"  name="goodsNum" id="goodsNum" value="1" class="layui-input" style="height:100%;width:50px; border:none;text-align:center">
            <div class="iconfont icon-tianjia1" onclick="compute2(1,1,1)"></div>
        </div>
        <div class="gray" style="padding-top: 2px;">@T("起订量"):1 @T("增量"):1</div>
        @{
            var materialTieredPrice = MaterialTieredPrice.GetPriceByMaterialIdAndQuantity(Model.Id, 10);
            var num = goodsPrice * 10;
            if (materialTieredPrice != null)
            {
                num = materialTieredPrice.Price * 10;
            }
            <div class="asideTextTitle totalPrice" id="totalPrice">@T("总价")：</div>
        }

        <div>
            <button class="button asideBtn" style="color: var(--blue-deep);" @(ViewBag.WareHouses.Count <= 0 ? "disabled" : "") onclick="addCart()">@T("加入购物车")</button>
        </div>
        <div style="padding: 0px;">
            <button class="button asideBtn button_blue" onclick="Buynow()" @(ViewBag.WareHouses.Count <= 0 ? "disabled" : "")>@T("立即购买")</button>
            <input type="hidden" name="records[0].skuId" id="skuId" />
            @*<form id="buyNowForm" action="@Url.Action("CheckOrder", "ShoppingCart")" method="post">
                <input type="hidden" name="records[0].goodsId" value="@Model.Id" />
                <input type="hidden" name="records[0].num" id="nums" value="1" />
                <input type="hidden" name="records[0].skuId" id="skuId" />
            </form>*@
        </div>

        <div id="wish" class="flex aside_btnBox2" style="justify-content: space-around;flex-wrap: wrap;padding-top: 1.2vw;">
            @if (ViewBag.IsWish)
            {
                <div onclick="delWishlist()" id="wishOpera"><i class="iconfont icon-aixin pointer" style="color: var(--blue-deep);"></i> @T("取消心愿单")</div>
            }
            else
            {
                <div onclick="addWishlist()" id="wishOpera"><i class="iconfont icon-aixin2 pointer" style="color: var(--blue-deep);"></i> @T("加入心愿单")</div>
            }
            <div><i class="iconfont icon-share pointer" style="color: var(--blue-deep);padding-right:0.5vw;"></i>@T("分享链接")</div>
        </div>
        <!-- 右侧边栏 -->
        <script>
            // function handleScroll() {
            //       //到顶 或者到底 就隐藏
            //       if (isAtTop() || isAtBottom()) {
            //           $('.orderAside').fadeOut(300);
            //       }else{
            //           $('.orderAside').fadeIn();
            //       }
            //       //处理滚动事件的逻辑
            //   }

            //   // 节流处理滚动事件
            //   const throttledScroll = throttle(handleScroll, 200);
            //   window.addEventListener('scroll', throttledScroll);
            // // document.addEventListener('mousewheel', function (e) {

            // // })
        </script>
    </aside>
    <!-- <img src="../../images/pics/01.png" class="img" alt=""> -->
    <!-- 服务申明 -->
    <div class="serverInfo">
        <div class="serverTitle" style="font-size: 1vw;">@T("服务申明")</div>
        <div class="serverTitle flex">
            <div class="iconfont icon-sign" style="font-size: 1.5vw;margin-left: .2vw;margin-right: .2vw;"></div>
            <div>@T("快递")</div>
        </div>
        <div class="serverItem flex">
            <div class="iconfont icon-ai210"></div><div>@T("支持七天无理由退货")</div>
        </div>
        <div class="serverItem flex">
            <div class="iconfont icon-ai210"></div><div>@T("如果快递丢失，支持退货")</div>
        </div>
        <div class="serverItem flex">
            <div class="iconfont icon-ai210"></div><div>@T("如果快递损坏，支持退货")</div>
        </div>
        <div class="serverItem flex">
            <div class="iconfont icon-ai210"></div><div>@T("支持90天内免费换货")</div>
        </div>

        <div class="serverTitle"> <i class="iconfont icon-secured" style="font-size: 1.5vw;"></i> @T("安全与隐私")</div>
        <div class="serverItem">
            <div>@T("安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。")</div>
            <div>@T("安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。")</div>
        </div>

        <div class="serverTitle">
            <i class="iconfont icon-money-circle" style="font-size: 1.5vw;"></i>
            @T("支付安全")
        </div>
        <div class="serverItem">
            <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
                <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
                <img src="../../images/icons/paypal.png" alt="">
                <img src="../../images/icons/weixin.png" alt="">
            </div>
            <div>@T("与受欢迎的支付合作伙伴合作，您的个人信息是安全的。")</div>
        </div>
    </div>


</body>

<script src="/public/script/lazyload.js"></script>
<script asp-location="Footer">
    var materialId = '@Model.MerchantMaterial';//默认物料编号
    var tieredPricesCount = @ViewBag.TieredPrices.Count//阶梯价格数量
    var currengPrice = @Model.GoodsPrice;//当前商品价格
    var pageEnterTime = Date.now();//记录进入页面的时间
    var goodsId = "@Model.Id";
    var originalPrice = "@marketPrice";

    
    window.addEventListener('load', function() {
        window.lazyLoadImages();
    });
    function Buynow()
    {
        var u = "@user";
        if(u == null | u == '')
        {
           window.location.href = '/Login';
        }
        else
        {
            
            // $("#nums").val(GoodsNum);
            // var form = document.getElementById('buyNowForm');
            // form.submit();
            var goodsNum = $("#goodsNum").val();
            var id = '@Model.Id';
            var skuId = $("#skuId").val();
            window.location.href = '@Url.Action("CheckOrder","ShoppingCart")?GId='+id+'&Num='+goodsNum+'&SkuId='+skuId;
        }
    }

    //加入购物车
    function addCart()
    {
        var GoodsId = "@Model.Id";
        var GoodsNum = parseInt($("#goodsNum").val());
        var skuId = $("#skuId").val();
        $.post("@Url.Action("AddCart","CubeHome")",{GoodsId,GoodsNum,skuId},function(res)
        {
            layui.layer.msg(res.msg);
            if (res.success) {
                // 如果添加成功，获取当前购物车数量
                if (typeof window.updateCartCount === 'function') {
                    window.updateCartCount(res.data);
                }
            }
        })
    }

    // 更新总价函数
    function updateTotalPrice(){
        var countElement = document.getElementById('goodsNum');
        var priceElement = document.getElementById('totalPrice');
        var currentPriceElement = document.getElementById('currentPrice');
        // 获取当前数量
        var currentNum = parseInt(countElement.value);
        console.log(currentNum);
        if(tieredPricesCount>1){//如果有阶梯价
            $.post('@Url.Action("GetTreTieredPrices", "Goods")', { materialId: materialId, number: currentNum }, function (res) {
                if (res.success) {
                     // 计算总价
                    var totalPrice = currentNum * res.data.Price;
                    // 显示总价，保留两位小数
                    priceElement.textContent = '@T("总价")：'+'@symbolLeft' + totalPrice.toFixed(2);
                    // 更新单价显示
                    //currentPriceElement.textContent = '@symbolLeft' + res.data.Price.toFixed(2);
                }
            });
        }else{
            // 计算总价
            var totalPrice = currentNum * currengPrice;
            // 显示总价，保留两位小数
            priceElement.textContent = '@T("总价")：'+'@symbolLeft' + totalPrice.toFixed(2);
            // 更新单价显示
            //currentPriceElement.textContent = '@symbolLeft' + currengPrice.toFixed(2);
        }
       
    }

    // 页面加载完毕后，初始化事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化显示正确的总价
        updateTotalPrice();
        var input = document.getElementById('goodsNum');
        input.addEventListener('input', function() {
            // 只保留数字
            this.value = this.value.replace(/[^0-9]/g, '');
            // 防止为空
            if (this.value === '' || parseInt(this.value) < 1) {
                this.value = 1;
            }
            updateTotalPrice();
        });
    });

    @* 加入心愿清单 *@
    function addWishlist()
    {
        var id = "@Model.Id";
        $.post('@Url.Action("AddWishlist","CubeHome")',{goodsId:id,sId:'@SId'},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                const wishlistBtn = document.querySelector("#wishOpera");
                wishlistBtn.innerHTML = `<i class="iconfont icon-aixin pointer" style="color: var(--blue-deep);"></i> @T("取消心愿单")`;
                wishlistBtn.onclick = function() {
                    delWishlist();
                };
            }
        })
    }
    @* 取消心愿清单 *@
    function delWishlist()
    {
        var id = "@Model.Id";
        $.post('@Url.Action("DelWishlist", "CubeHome")',{goodsId:id},function(res)
        {
            layui.layer.msg(res.msg);
            if(res.success)
            {
                const wishlistBtn = document.querySelector("#wishOpera");
                wishlistBtn.innerHTML = `<i class="iconfont icon-aixin2 pointer" style="color: var(--blue-deep);"></i> @T("加入心愿单")`;
                wishlistBtn.onclick = function() {
                    addWishlist();
                };
            }
        })
    }

    function uploadViewDuration() {
        var duration = Math.floor((Date.now() - pageEnterTime) / 1000); // 秒
        // 可根据需要传递商品ID等参数
        $.post('@Url.Action("RecordViewDuration", "Goods")', {
            goodsId: '@Model.Id',
            duration: duration
        });
    }
    window.addEventListener('beforeunload', function (e) {
        uploadViewDuration();
    });
</script>
<script>
    // 存储选中的规格值
let selectedSpecValues = {};

var maxCount = 0;

// 初始化规格选择
$(document).ready(function() {
    // 为规格选择添加点击事件
    $('.configBox div[data-value-id]').on('click', handleSpecSelect);

    // 触发默认选中的规格值
    $('.configBox div[data-select="true"]').each(function() {
        handleSpecSelect.call(this);
    });

    GetSimilarProducts('@Model.CId',0,'');

    

    setTimeout(function(){
        $.getJSON('@Url.Action("GetGoodsNum")',{gid:'@Model.Id',skuid:$("#skuId").val()},function(res){
            maxCount = res.data;
        })
    },300);


});

    // 计算器函数，处理增减
    function compute2(isAdd, step, min) {
        console.log('compute2');
        var countElement = document.getElementById('goodsNum');
        var currentCount = parseInt(countElement.value);
        var newCount;
        if (isAdd) {
            newCount = currentCount + step;
        } else {
            newCount = currentCount - step;
        // 确保不小于最小购买量
            if (newCount < min) newCount = min;
        }

        if(newCount > maxCount){
            newCount = maxCount;
        }
        // 更新数量显示
        countElement.value = newCount;
        // 更新总价
        updateTotalPrice();
    }

// 处理规格选择
function handleSpecSelect() {
    const $this = $(this);
    const specId = $this.data('spec-id');
    const valueId = $this.data('value-id');

    // 更新选中状态
    selectedSpecValues[specId] = valueId;

    // 检查是否所有规格都已选择
    const allSpecsSelected = $('.configBox').length === Object.keys(selectedSpecValues).length;

    if (allSpecsSelected) {
        // 获取所有选中的规格值ID
        const selectedIds = Object.values(selectedSpecValues);
        var priceElement = document.getElementById('totalPrice');
        var countElement = document.getElementById('goodsNum');//获取数量
        var currentNum = parseInt(countElement.value);

        // 调用后端获取价格
        $.post('@Url.Action("GetGoodsSkuDetail", "Goods")', {
            goodsId: '@Model.Id',
            specValueIds: selectedIds,
            number: currentNum
        }, function(result) {
            if (result.success) {
                materialId = result.data.MaterialId; //当前选中的物料编号
                // 更新价格显示
                $('#skuId').val(result.data.Id);
                $('#skuMarketPrice').val(result.data.GoodsMarketPrice);
                $('#skuPrice').val(result.data.GoodsPrice);
                //更新价格显示
                // 计算总价
                var totalPrice = currentNum * result.data.GoodsPrice;
                // 显示总价，保留两位小数
                priceElement.textContent = '@T("总价")：'+'@symbolLeft' + totalPrice.toFixed(2);
                // 更新单价显示
                var currentPriceElement = document.getElementById('currentPrice');
                //currentPriceElement.textContent = '@symbolLeft' + result.data.GoodsPrice.toFixed(2);
                 // 更新库存显示
                if (result.extdata.wareHouseMaterials) {
                    const table = $('#wareHouseTable');
                    const thead = table.find('thead tr');
                    const tbody = table.find('tbody tr');
                    thead.empty();
                    tbody.empty();
                    if ( result.extdata.wareHouseMaterials.length > 0) {
                        // 更新表头和内容
                        result.extdata.wareHouseMaterials.forEach(warehouse => {
                            thead.append(`<th style="padding: 0.2vw;">${warehouse.WareHouseName}</th>`);
                            tbody.append(`<th style="padding: 0.2vw;">${warehouse.Quantity}</th>`);
                        });
                        if(result.extdata.materialNum>0){
                            // 启用按钮
                            $('.asideBtn').prop('disabled', false);
                            $('.asideBtn').css('opacity', '1');
                        }else{
                            // 禁用按钮
                            $('.asideBtn').prop('disabled', true);
                            $('.asideBtn').css('opacity', '0.5');
                        }
                    } else {
                        thead.append(`<th style="padding: 0.2vw;">@T("暂无库存")</th>`);
                        // 禁用按钮
                        $('.asideBtn').prop('disabled', true);
                        $('.asideBtn').css('opacity', '0.5');
                    }
                }
                //更新阶梯价显示
                tieredPricesCount = result.extdata.tieredPrices.length;

                if (result.extdata.tieredPrices) {
                    var $tbody = $('.tableBox table').eq(0).find('tbody'); // 找到第一个阶梯价表格的tbody
                    $tbody.empty();
                    if ( result.extdata.tieredPrices.length > 0) {
                       var lastIndex = result.extdata.tieredPrices.length - 1;
                        result.extdata.tieredPrices.forEach(function(tier, i) {
                            var min = tier.MinQuantity;
                            var max = tier.MaxQuantity > 0 ? ' ~ ' + tier.MaxQuantity : (i === lastIndex ? '~' : '');
                            var row = `<tr>
                                <td>${min}+</td>
                                <td>@symbolLeft${parseFloat(tier.OriginalPrice).toFixed(2)}</td>
                                <td>@symbolLeft${parseFloat(tier.Price).toFixed(2)}</td>
                            </tr>`;
                            $tbody.append(row);
                        });
                    } else {
                        $tbody.append(`<tr>
                            <td>1+</td>
                            <td>@symbolLeft${parseFloat(result.data.GoodsMarketPrice).toFixed(2)}</td>
                            <td>@symbolLeft${parseFloat(result.data.GoodsPrice).toFixed(2)}</td>
                        </tr>`);
                    }
                }
            }
        });
    }
}

</script>
<link href="~/public/static/plugins/plyr/plyr.css" rel="stylesheet" />
<script src="~/public/static/plugins/plyr/plyr.js"></script>
<script>
    const player = new Plyr('#player', {
        controls: ['play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
         ratio: '6:9',
         resetOnEnd: false,
    });
function toggleVideo() {
   const videoOverlay = document.querySelector('.video-overlay');
    if (videoOverlay) {
        if (videoOverlay.style.display === 'none' || !videoOverlay.style.display) {
            // 显示视频
            videoOverlay.style.display = 'block';
            // 播放视频
            if (player) {
                player.play();
            }
        } else {
            // 隐藏视频
            videoOverlay.style.display = 'none';
            // 暂停视频
            if (player) {
                player.pause();
            }
        }
    }
}
function switchMainImage(element) {
    const thumbnailImage = element.querySelector('img');
    const mainImage = document.getElementById('mainImage');
    mainImage.src = thumbnailImage.src;

    // 更新选中状态
    document.querySelectorAll('.thumbnail-item').forEach(item => {
        item.classList.remove('active');
        item.classList.remove('imgBorder'); // 移除所有缩略图的红色边框
    });
    element.classList.add('active');
    element.classList.add('imgBorder'); // 为当前选中的缩略图添加红色边框
    // 隐藏视频
    const videoOverlay = document.querySelector('.video-overlay');
    if (videoOverlay) {
        videoOverlay.style.display = 'none';
        // 如果视频正在播放，暂停视频
        if (player) {
            player.pause();
        }
    }
}
</script>
<script>
        function titleClick(dom,index) {
            $(dom).siblings().removeClass('titleSelect');
            $(dom).addClass('titleSelect');
            if ( index == 1) {
            $('.optionsBox2').nextUntil('.goodsDataTitle1').hide();
            if ($('.goodsDataTitle1')[0].style.display === 'none') {
            $('.commentBox').nextUntil('.bug').show();
            }
            }else{
            $('.commentBox').nextUntil('.bug').hide();
            if ($('.commentHeader')[0].style.display === 'none') {
            $('.optionsBox2').nextUntil('.goodsDataTitle1').show();
            }
            }

        }
    </script>
    <script>
        //下载pdf
        const pdfFrame = document.getElementById('pdfFrame');
        @* console.log('pdfFrame :>> ', pdfFrame); *@
    function downloadPDF() {
        if (pdfFrame) {
            const pdfUrl = pdfFrame.src;
            // 在新标签页中打开PDF
            window.open(pdfUrl, '_blank');
        }
    }
</script>
<script>
layui.use(['form'], function(){
    var form = layui.form;
    form.on('checkbox', function(data){
        var jsonString;//获取选中行
        var name = $(data.elem).attr('name');
        var checked = data.elem.checked;
        var storeId = 0;
        if(name == 'checkCompanyName' && checked){
            storeId = '@Model.StoreId'; // 获取店铺ID
        } 
       
        // 全选
        var elem = data.elem;
        if($(elem).attr('name') === 'selectAll'){
            var checked = elem.checked;
            $('.goodsDataTable tbody input[type="checkbox"]:not(:disabled)').prop('checked', checked);
            layui.form.render('checkbox');
            jsonString = getCheckedData()
            console.log('111',jsonString)
        }
        // 取消全选联动
        // 只处理tbody里的checkbox
        if ($(elem).closest('tbody').length > 0) {
            var $all = $('.goodsDataTable tbody input[type="checkbox"]:not(:disabled)');
            var $checked = $all.filter(':checked');
            var isAllChecked = $all.length > 0 && $all.length === $checked.length;
            $('.goodsDataTable thead input[name="selectAll"]').prop('checked', isAllChecked);
            form.render('checkbox');
            jsonString = getCheckedData();
            console.log('222', jsonString);
        }

         // 调用接口
        GetSimilarProducts('@Model.CId', storeId, jsonString);


    });
});
function GetSimilarProducts(cId,storeId,jsonKey){
    $.post('@Url.Action("GetSimilarProducts")', {
        cId: cId,
        storeId: storeId,
        jsonKey: jsonKey
    }, 
    function(res){
        if (res.success) {
            $('#SimilarNum').text(res.msg);
            $("#Similarhref").attr("href", res.data);
        }else {
            $('#SimilarNum').text("0");
            $("#Similarhref").attr("href", "javascript:void(0);");
        }
    });
}


function getCheckedData() {
    var checkedList = [];
    // 遍历tbody下所有已选中的且未禁用的checkbox
    $('.goodsDataTable tbody input[type="checkbox"]:checked:not(:disabled)').each(function(){
        var name = $(this).attr('name');
        var value = $(this).val();
        console.log('333', name,value);
        if (name.indexOf('Field') !== -1){
            checkedList.push({
                id: name,
                value: value,
            });
        }; 
        
    });
    return JSON.stringify(checkedList);
}
</script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化显示正确的总价
    updateTotalPrice();

    var input = document.getElementById('goodsNum');
    input.addEventListener('input', function() {
        // 只保留数字
        this.value = this.value.replace(/[^0-9]/g, '');
        // 防止为空
        if (this.value === '' || parseInt(this.value) < 1) {
            this.value = 1;
        }

        // 获取当前数量
        var currentCount = parseInt(this.value);
        // 获取最大值
        var maxCount = parseInt('@ViewBag.Inventory'); // 假设最大值存储在 ViewBag.Inventory 中

        // 确保不大于最大购买量
        if (currentCount > maxCount) {
            this.value = maxCount;
        }

        updateTotalPrice();
    });
});
</script>
<style>
    .container {
        max-width: 800px;
        margin: 20px auto;
        text-align: center;
    }
    .input-container {
        margin-bottom: 20px;
    }
    #videoUrl {
        padding: 10px;
        margin-right: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    #loadBtn {
        padding: 10px 20px;
        background-color: #00adef;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    #loadBtn:hover {
        background-color: #0089bd;
    }
    .plyr__video-embed {
        width: 70%;
        height: 70%;
        margin: 0 auto;
    }
    .asideBtn[disabled] {
        opacity: 0.5;
        cursor: not-allowed;
    }
 .layui-input:focus,
 .layui-textarea:focus {
  border-color: none !important; 
  box-shadow: none !important;  
}
#Similarhref {
    color: #1a73e8;
    cursor: pointer;
    transition: color 0.2s;
}
#Similarhref:hover {
    text-decoration: underline;
}

</style>

