﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using B2B2CShop.Dto;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using Newtonsoft.Json;
using Pek;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace B2B2CShop.Entity;

public partial class GoodsSKUDetail : DHEntityBase<GoodsSKUDetail>
{
    #region 对象操作
    static GoodsSKUDetail()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(GoodsId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化GoodsSKUDetail[商品SKU属性表]数据……");

    //    var entity = new GoodsSKUDetail();
    //    entity.Id = 0;
    //    entity.GoodsId = 0;
    //    entity.SpecValue = "abc";
    //    entity.MaterialId = 0;
    //    entity.GoodsMarketPrice = 0.0;
    //    entity.GoodsPrice = 0.0;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化GoodsSKUDetail[商品SKU属性表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>商品编号</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Goods? Goods => Extends.Get(nameof(Goods), k => Goods.FindById(GoodsId));

    /// <summary>商品编号</summary>
    [Map(nameof(GoodsId), typeof(Goods), "Id")]
    public String? GoodsName => Goods?.Name;
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public List<GoodsSpecDto> goodsSpecDto => JsonConvert.DeserializeObject<List<GoodsSpecDto>>(SpecValue??"")??[];
    /// <summary>
    /// 规格名称集合
    /// </summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public List<int> specIDs => goodsSpecDto.Select(x => x.Id).Distinct().ToList();
    public string SpecValueDetail(int lid)
    {
        string detail = "";
        foreach (var item in goodsSpecDto)
        {
            detail += (GoodsSpecValueLan.FindBySIdAndLId(item.VId, lid)?.LanName ?? item.Value) + "  ";
        }
        return detail;
    }
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="goodsId">商品编号</param>
    /// <param name="materialId">物料编号</param>
    /// <param name="start">编号开始</param>
    /// <param name="end">编号结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<GoodsSKUDetail> Search(Int64 goodsId, Int64 materialId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (goodsId >= 0) exp &= _.GoodsId == goodsId;
        if (materialId >= 0) exp &= _.MaterialId == materialId;
        exp &= _.Id.Between(start, end, Meta.Factory.Snow);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }
    public static IList<GoodsSKUDetail> FindAllByGoodsId(Int64 goodsId)
    {
        var exp = new WhereExpression();

        return FindAll(_.GoodsId == goodsId);
    }
    public static GoodsSKUDetail? FindBySpecvalueAndGId(string[] sepc, Int64 goodsId)
    {
        if (sepc.Length == 0) return null;
        if (goodsId.IsNull()) return null;
        var exp = new WhereExpression();
        exp &= _.GoodsId == goodsId;
        foreach (var item in sepc)
        {
            exp &= _.SpecValue.Contains(item);
        }
        return Find(exp);
    }
    public static IList<GoodsSKUDetail> FindAllByIds(string ids)
    {
        if (ids.IsNullOrEmpty()) return [];

        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(x => ids.Split(",").Contains(x.Id.SafeString()));

        return FindAll(_.Id.In(ids.Split(",")));
    }
    // Select Count(Id) as Id,Category From DH_GoodsSKUDetail Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<GoodsSKUDetail> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IGoodsSKUDetail ToModel()
    {
        var model = new GoodsSKUDetail();
        model.Copy(this);

        return model;
    }
    public static void DeleteOther(Int64 goodsid, string skuIds)
    {
        Delete(_.GoodsId == goodsid & _.Id.NotIn(skuIds.Split(',')));
        GoodsTieredPrice.Delete(GoodsTieredPrice._.GoodsId == goodsid & GoodsTieredPrice._.SkuId.NotIn(skuIds.Split(',')));
        GoodsSKUDetail.Meta.Cache.Clear("", true);
        GoodsTieredPrice.Meta.Cache.Clear("", true);

    }
    public static void DeleteByGIds(string gids)
    {
        Delete(_.GoodsId.In(gids.Split(",")));
        Meta.Cache.Clear("", true);
    }
    #endregion
}
