.service {
  width: 960px;
  margin: 0 auto;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
}

.top li {
  display: flex;
  align-items: center;
}

.top li img {
  width: 52px;
  height: 52px;
  margin-right: 10px;
}

.top .wen {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.wen span {
  font-size: 20px;
  color: var(--blue-deep);
}

.wen span:first-child {
  font-size: 16px;
  color: var(--text-color);
}

.wen .day {
  font-size: 16px;
  color: var(--text-color3);
}

.service main {
  height: 626px;
  background: #FFFFFF;
  box-shadow: 4px 4px 4px 0px rgba(44, 121, 232, 0.1), -4px -4px 4px 0px rgba(44, 121, 232, 0.1);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #D9D9D9;

}

.service h3 {
  text-align: center;
  margin: 30px 0;
  font-size: 28px;
  font-weight: normal;
}
.btnBox{
  display: flex;
  justify-content: center;
  margin-top: 60px;
}
.service .btn {
  width: 100px;
  height: 40px;
  border-radius: 8px;
}