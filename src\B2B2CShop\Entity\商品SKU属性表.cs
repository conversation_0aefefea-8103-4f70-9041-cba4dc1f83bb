﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace B2B2CShop.Entity;

/// <summary>商品SKU属性表</summary>
[Serializable]
[DataObject]
[Description("商品SKU属性表")]
[BindIndex("IU_DH_GoodsSKUDetail_GoodsId_MaterialId", true, "GoodsId,MaterialId")]
[BindTable("DH_GoodsSKUDetail", Description = "商品SKU属性表", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class GoodsSKUDetail : IGoodsSKUDetail, IEntity<IGoodsSKUDetail>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, false, false, 0)]
    [BindColumn("Id", "编号", "", DataScale = "time")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int64 _GoodsId;
    /// <summary>商品编号</summary>
    [DisplayName("商品编号")]
    [Description("商品编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("GoodsId", "商品编号", "")]
    public Int64 GoodsId { get => _GoodsId; set { if (OnPropertyChanging("GoodsId", value)) { _GoodsId = value; OnPropertyChanged("GoodsId"); } } }

    private String? _SpecValue;
    /// <summary>规格值</summary>
    [DisplayName("规格值")]
    [Description("规格值")]
    [DataObjectField(false, false, true, 500)]
    [BindColumn("SpecValue", "规格值", "")]
    public String? SpecValue { get => _SpecValue; set { if (OnPropertyChanging("SpecValue", value)) { _SpecValue = value; OnPropertyChanged("SpecValue"); } } }

    private Int64 _MaterialId;
    /// <summary>物料编号</summary>
    [DisplayName("物料编号")]
    [Description("物料编号")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("MaterialId", "物料编号", "")]
    public Int64 MaterialId { get => _MaterialId; set { if (OnPropertyChanging("MaterialId", value)) { _MaterialId = value; OnPropertyChanged("MaterialId"); } } }

    private Decimal _GoodsMarketPrice;
    /// <summary>市场价</summary>
    [DisplayName("市场价")]
    [Description("市场价")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("GoodsMarketPrice", "市场价", "")]
    public Decimal GoodsMarketPrice { get => _GoodsMarketPrice; set { if (OnPropertyChanging("GoodsMarketPrice", value)) { _GoodsMarketPrice = value; OnPropertyChanged("GoodsMarketPrice"); } } }

    private Decimal _GoodsPrice;
    /// <summary>单价</summary>
    [DisplayName("单价")]
    [Description("单价")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("GoodsPrice", "单价", "")]
    public Decimal GoodsPrice { get => _GoodsPrice; set { if (OnPropertyChanging("GoodsPrice", value)) { _GoodsPrice = value; OnPropertyChanged("GoodsPrice"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IGoodsSKUDetail model)
    {
        Id = model.Id;
        GoodsId = model.GoodsId;
        SpecValue = model.SpecValue;
        MaterialId = model.MaterialId;
        GoodsMarketPrice = model.GoodsMarketPrice;
        GoodsPrice = model.GoodsPrice;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "GoodsId" => _GoodsId,
            "SpecValue" => _SpecValue,
            "MaterialId" => _MaterialId,
            "GoodsMarketPrice" => _GoodsMarketPrice,
            "GoodsPrice" => _GoodsPrice,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "GoodsId": _GoodsId = value.ToLong(); break;
                case "SpecValue": _SpecValue = Convert.ToString(value); break;
                case "MaterialId": _MaterialId = value.ToLong(); break;
                case "GoodsMarketPrice": _GoodsMarketPrice = Convert.ToDecimal(value); break;
                case "GoodsPrice": _GoodsPrice = Convert.ToDecimal(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static GoodsSKUDetail? FindById(Int64 id)
    {
        if (id < 0) return null;

        return Find(_.Id == id);
    }

    /// <summary>根据商品编号、物料编号查找</summary>
    /// <param name="goodsId">商品编号</param>
    /// <param name="materialId">物料编号</param>
    /// <returns>实体对象</returns>
    public static GoodsSKUDetail? FindByGoodsIdAndMaterialId(Int64 goodsId, Int64 materialId)
    {
        if (goodsId < 0) return null;
        if (materialId < 0) return null;

        return Find(_.GoodsId == goodsId & _.MaterialId == materialId);
    }
    #endregion

    #region 数据清理
    /// <summary>清理指定时间段内的数据</summary>
    /// <param name="start">开始时间。未指定时清理小于指定时间的所有数据</param>
    /// <param name="end">结束时间</param>
    /// <returns>清理行数</returns>
    public static Int32 DeleteWith(DateTime start, DateTime end)
    {
        return Delete(_.Id.Between(start, end, Meta.Factory.Snow));
    }
    #endregion

    #region 字段名
    /// <summary>取得商品SKU属性表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>商品编号</summary>
        public static readonly Field GoodsId = FindByName("GoodsId");

        /// <summary>规格值</summary>
        public static readonly Field SpecValue = FindByName("SpecValue");

        /// <summary>物料编号</summary>
        public static readonly Field MaterialId = FindByName("MaterialId");

        /// <summary>市场价</summary>
        public static readonly Field GoodsMarketPrice = FindByName("GoodsMarketPrice");

        /// <summary>单价</summary>
        public static readonly Field GoodsPrice = FindByName("GoodsPrice");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得商品SKU属性表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>商品编号</summary>
        public const String GoodsId = "GoodsId";

        /// <summary>规格值</summary>
        public const String SpecValue = "SpecValue";

        /// <summary>物料编号</summary>
        public const String MaterialId = "MaterialId";

        /// <summary>市场价</summary>
        public const String GoodsMarketPrice = "GoodsMarketPrice";

        /// <summary>单价</summary>
        public const String GoodsPrice = "GoodsPrice";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
