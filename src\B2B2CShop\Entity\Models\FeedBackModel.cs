﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace B2B2CShop.Entity;

/// <summary>意见反馈</summary>
public partial class FeedBackModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int32 Id { get; set; }

    /// <summary>姓名</summary>
    public String? Name { get; set; }

    /// <summary>电话</summary>
    public String? Phone { get; set; }

    /// <summary>邮箱</summary>
    public String? Mail { get; set; }

    /// <summary>公司名称</summary>
    public String? CompanyName { get; set; }

    /// <summary>主题</summary>
    public String? Theme { get; set; }

    /// <summary>国家</summary>
    public String? Country { get; set; }

    /// <summary>反馈内容</summary>
    public String? Content { get; set; }

    /// <summary>1:手机端 2:PC端</summary>
    public Int16 FType { get; set; }

    /// <summary>会员ID</summary>
    public Int32 UId { get; set; }

    /// <summary>会员名称</summary>
    public String UName { get; set; } = null!;

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IFeedBack model)
    {
        Id = model.Id;
        Name = model.Name;
        Phone = model.Phone;
        Mail = model.Mail;
        CompanyName = model.CompanyName;
        Theme = model.Theme;
        Country = model.Country;
        Content = model.Content;
        FType = model.FType;
        UId = model.UId;
        UName = model.UName;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion
}
