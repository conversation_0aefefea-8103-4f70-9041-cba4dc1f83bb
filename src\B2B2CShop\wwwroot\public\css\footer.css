/* 底部样式 -公共 */
.footer {
    width: 100%;
    background-color: #445268;
    position: relative;
    /* border: 2px solid red; */
}
.footer1 {
    color: white;
    /* padding: 20px 40px; */
    padding: 20px 10vw;
    display: flex;
    justify-content: space-between;
}
.footer1 h3 {
    margin-top: 0;
}
.footer1 ul {
    list-style-type: none;
    padding: 0;
    margin-top: 20px;
}

.footer1 ul li {
    margin: 5px 0;
    color: #BBBBBB;
    margin-bottom: 10px;
}
.footer1 a {
    color: white;
    text-decoration: none;
}
.social-icons img {
    width: 20px;
    height: 20px;
    margin-top: 20px;
}
.qr-code {
    width: 100px;
    height: 100px;
    margin-top: 10px;
    margin-bottom: 10px;
}
.one {
    width: 320px;
    margin-right: 30px;
}
.two, .three, .four {
    width: 200px;
}
.five {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    width: 200px;
}