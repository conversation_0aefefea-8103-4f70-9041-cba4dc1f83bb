﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace B2B2CShop.Entity;

/// <summary>商品阶梯价格表</summary>
public partial class GoodsTieredPriceModel
{
    #region 属性
    /// <summary>编号</summary>
    public Int64 Id { get; set; }

    /// <summary>商品ID</summary>
    public Int64 GoodsId { get; set; }

    /// <summary>商品公共表ID</summary>
    public Int64 GoodsCommonId { get; set; }

    /// <summary>店铺ID</summary>
    public Int64 StoreId { get; set; }

    /// <summary>商品SkuID</summary>
    public Int64 SkuId { get; set; }

    /// <summary>最小购买数量</summary>
    public Int32 MinQuantity { get; set; }

    /// <summary>最大购买数量</summary>
    public Int32 MaxQuantity { get; set; }

    /// <summary>原价</summary>
    public Decimal OriginalPrice { get; set; }

    /// <summary>销售价</summary>
    public Decimal Price { get; set; }

    /// <summary>是否启用</summary>
    public Boolean Enabled { get; set; }

    /// <summary>创建者</summary>
    public String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    public Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    public DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    public String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    public String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    public Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    public String? UpdateIP { get; set; }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IGoodsTieredPrice model)
    {
        Id = model.Id;
        GoodsId = model.GoodsId;
        GoodsCommonId = model.GoodsCommonId;
        StoreId = model.StoreId;
        SkuId = model.SkuId;
        MinQuantity = model.MinQuantity;
        MaxQuantity = model.MaxQuantity;
        OriginalPrice = model.OriginalPrice;
        Price = model.Price;
        Enabled = model.Enabled;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion
}
