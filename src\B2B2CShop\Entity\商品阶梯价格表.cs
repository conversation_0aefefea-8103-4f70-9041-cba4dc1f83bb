﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace B2B2CShop.Entity;

/// <summary>商品阶梯价格表</summary>
[Serializable]
[DataObject]
[Description("商品阶梯价格表")]
[BindIndex("IX_DH_GoodsTieredPrice_GoodsId", false, "GoodsId")]
[BindIndex("IX_DH_GoodsTieredPrice_GoodsCommonId", false, "GoodsCommonId")]
[BindIndex("IX_DH_GoodsTieredPrice_StoreId", false, "StoreId")]
[BindIndex("IX_DH_GoodsTieredPrice_SkuId", false, "SkuId")]
[BindIndex("IU_DH_GoodsTieredPrice_SkuId_MinQuantity", true, "SkuId,MinQuantity")]
[BindTable("DH_GoodsTieredPrice", Description = "商品阶梯价格表", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class GoodsTieredPrice : IGoodsTieredPrice, IEntity<IGoodsTieredPrice>
{
    #region 属性
    private Int64 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int64 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private Int64 _GoodsId;
    /// <summary>商品ID</summary>
    [DisplayName("商品ID")]
    [Description("商品ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("GoodsId", "商品ID", "")]
    public Int64 GoodsId { get => _GoodsId; set { if (OnPropertyChanging("GoodsId", value)) { _GoodsId = value; OnPropertyChanged("GoodsId"); } } }

    private Int64 _GoodsCommonId;
    /// <summary>商品公共表ID</summary>
    [DisplayName("商品公共表ID")]
    [Description("商品公共表ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("GoodsCommonId", "商品公共表ID", "")]
    public Int64 GoodsCommonId { get => _GoodsCommonId; set { if (OnPropertyChanging("GoodsCommonId", value)) { _GoodsCommonId = value; OnPropertyChanged("GoodsCommonId"); } } }

    private Int64 _StoreId;
    /// <summary>店铺ID</summary>
    [DisplayName("店铺ID")]
    [Description("店铺ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("StoreId", "店铺ID", "")]
    public Int64 StoreId { get => _StoreId; set { if (OnPropertyChanging("StoreId", value)) { _StoreId = value; OnPropertyChanged("StoreId"); } } }

    private Int64 _SkuId;
    /// <summary>商品SkuID</summary>
    [DisplayName("商品SkuID")]
    [Description("商品SkuID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("SkuId", "商品SkuID", "")]
    public Int64 SkuId { get => _SkuId; set { if (OnPropertyChanging("SkuId", value)) { _SkuId = value; OnPropertyChanged("SkuId"); } } }

    private Int32 _MinQuantity;
    /// <summary>最小购买数量</summary>
    [DisplayName("最小购买数量")]
    [Description("最小购买数量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("MinQuantity", "最小购买数量", "")]
    public Int32 MinQuantity { get => _MinQuantity; set { if (OnPropertyChanging("MinQuantity", value)) { _MinQuantity = value; OnPropertyChanged("MinQuantity"); } } }

    private Int32 _MaxQuantity;
    /// <summary>最大购买数量</summary>
    [DisplayName("最大购买数量")]
    [Description("最大购买数量")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("MaxQuantity", "最大购买数量", "")]
    public Int32 MaxQuantity { get => _MaxQuantity; set { if (OnPropertyChanging("MaxQuantity", value)) { _MaxQuantity = value; OnPropertyChanged("MaxQuantity"); } } }

    private Decimal _OriginalPrice;
    /// <summary>原价</summary>
    [DisplayName("原价")]
    [Description("原价")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("OriginalPrice", "原价", "")]
    public Decimal OriginalPrice { get => _OriginalPrice; set { if (OnPropertyChanging("OriginalPrice", value)) { _OriginalPrice = value; OnPropertyChanged("OriginalPrice"); } } }

    private Decimal _Price;
    /// <summary>销售价</summary>
    [DisplayName("销售价")]
    [Description("销售价")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Price", "销售价", "")]
    public Decimal Price { get => _Price; set { if (OnPropertyChanging("Price", value)) { _Price = value; OnPropertyChanged("Price"); } } }

    private Boolean _Enabled;
    /// <summary>是否启用</summary>
    [DisplayName("是否启用")]
    [Description("是否启用")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("Enabled", "是否启用", "", DefaultValue = "True")]
    public Boolean Enabled { get => _Enabled; set { if (OnPropertyChanging("Enabled", value)) { _Enabled = value; OnPropertyChanged("Enabled"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }

    private String? _UpdateUser;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateUser", "更新者", "")]
    public String? UpdateUser { get => _UpdateUser; set { if (OnPropertyChanging("UpdateUser", value)) { _UpdateUser = value; OnPropertyChanged("UpdateUser"); } } }

    private Int32 _UpdateUserID;
    /// <summary>更新者</summary>
    [DisplayName("更新者")]
    [Description("更新者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UpdateUserID", "更新者", "")]
    public Int32 UpdateUserID { get => _UpdateUserID; set { if (OnPropertyChanging("UpdateUserID", value)) { _UpdateUserID = value; OnPropertyChanged("UpdateUserID"); } } }

    private DateTime _UpdateTime;
    /// <summary>更新时间</summary>
    [DisplayName("更新时间")]
    [Description("更新时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("UpdateTime", "更新时间", "")]
    public DateTime UpdateTime { get => _UpdateTime; set { if (OnPropertyChanging("UpdateTime", value)) { _UpdateTime = value; OnPropertyChanged("UpdateTime"); } } }

    private String? _UpdateIP;
    /// <summary>更新地址</summary>
    [DisplayName("更新地址")]
    [Description("更新地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("UpdateIP", "更新地址", "")]
    public String? UpdateIP { get => _UpdateIP; set { if (OnPropertyChanging("UpdateIP", value)) { _UpdateIP = value; OnPropertyChanged("UpdateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IGoodsTieredPrice model)
    {
        Id = model.Id;
        GoodsId = model.GoodsId;
        GoodsCommonId = model.GoodsCommonId;
        StoreId = model.StoreId;
        SkuId = model.SkuId;
        MinQuantity = model.MinQuantity;
        MaxQuantity = model.MaxQuantity;
        OriginalPrice = model.OriginalPrice;
        Price = model.Price;
        Enabled = model.Enabled;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
        UpdateUser = model.UpdateUser;
        UpdateUserID = model.UpdateUserID;
        UpdateTime = model.UpdateTime;
        UpdateIP = model.UpdateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "GoodsId" => _GoodsId,
            "GoodsCommonId" => _GoodsCommonId,
            "StoreId" => _StoreId,
            "SkuId" => _SkuId,
            "MinQuantity" => _MinQuantity,
            "MaxQuantity" => _MaxQuantity,
            "OriginalPrice" => _OriginalPrice,
            "Price" => _Price,
            "Enabled" => _Enabled,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            "UpdateUser" => _UpdateUser,
            "UpdateUserID" => _UpdateUserID,
            "UpdateTime" => _UpdateTime,
            "UpdateIP" => _UpdateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToLong(); break;
                case "GoodsId": _GoodsId = value.ToLong(); break;
                case "GoodsCommonId": _GoodsCommonId = value.ToLong(); break;
                case "StoreId": _StoreId = value.ToLong(); break;
                case "SkuId": _SkuId = value.ToLong(); break;
                case "MinQuantity": _MinQuantity = value.ToInt(); break;
                case "MaxQuantity": _MaxQuantity = value.ToInt(); break;
                case "OriginalPrice": _OriginalPrice = Convert.ToDecimal(value); break;
                case "Price": _Price = Convert.ToDecimal(value); break;
                case "Enabled": _Enabled = value.ToBoolean(); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                case "UpdateUser": _UpdateUser = Convert.ToString(value); break;
                case "UpdateUserID": _UpdateUserID = value.ToInt(); break;
                case "UpdateTime": _UpdateTime = value.ToDateTime(); break;
                case "UpdateIP": _UpdateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static GoodsTieredPrice? FindById(Int64 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据商品ID查找</summary>
    /// <param name="goodsId">商品ID</param>
    /// <returns>实体列表</returns>
    public static IList<GoodsTieredPrice> FindAllByGoodsId(Int64 goodsId)
    {
        if (goodsId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.GoodsId == goodsId);

        return FindAll(_.GoodsId == goodsId);
    }

    /// <summary>根据商品公共表ID查找</summary>
    /// <param name="goodsCommonId">商品公共表ID</param>
    /// <returns>实体列表</returns>
    public static IList<GoodsTieredPrice> FindAllByGoodsCommonId(Int64 goodsCommonId)
    {
        if (goodsCommonId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.GoodsCommonId == goodsCommonId);

        return FindAll(_.GoodsCommonId == goodsCommonId);
    }

    /// <summary>根据店铺ID查找</summary>
    /// <param name="storeId">店铺ID</param>
    /// <returns>实体列表</returns>
    public static IList<GoodsTieredPrice> FindAllByStoreId(Int64 storeId)
    {
        if (storeId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.StoreId == storeId);

        return FindAll(_.StoreId == storeId);
    }

    /// <summary>根据商品SkuID查找</summary>
    /// <param name="skuId">商品SkuID</param>
    /// <returns>实体列表</returns>
    public static IList<GoodsTieredPrice> FindAllBySkuId(Int64 skuId)
    {
        if (skuId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.SkuId == skuId);

        return FindAll(_.SkuId == skuId);
    }

    /// <summary>根据商品SkuID、最小购买数量查找</summary>
    /// <param name="skuId">商品SkuID</param>
    /// <param name="minQuantity">最小购买数量</param>
    /// <returns>实体对象</returns>
    public static GoodsTieredPrice? FindBySkuIdAndMinQuantity(Int64 skuId, Int32 minQuantity)
    {
        if (skuId < 0) return null;
        if (minQuantity < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.SkuId == skuId && e.MinQuantity == minQuantity);

        return Find(_.SkuId == skuId & _.MinQuantity == minQuantity);
    }
    #endregion

    #region 字段名
    /// <summary>取得商品阶梯价格表字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>商品ID</summary>
        public static readonly Field GoodsId = FindByName("GoodsId");

        /// <summary>商品公共表ID</summary>
        public static readonly Field GoodsCommonId = FindByName("GoodsCommonId");

        /// <summary>店铺ID</summary>
        public static readonly Field StoreId = FindByName("StoreId");

        /// <summary>商品SkuID</summary>
        public static readonly Field SkuId = FindByName("SkuId");

        /// <summary>最小购买数量</summary>
        public static readonly Field MinQuantity = FindByName("MinQuantity");

        /// <summary>最大购买数量</summary>
        public static readonly Field MaxQuantity = FindByName("MaxQuantity");

        /// <summary>原价</summary>
        public static readonly Field OriginalPrice = FindByName("OriginalPrice");

        /// <summary>销售价</summary>
        public static readonly Field Price = FindByName("Price");

        /// <summary>是否启用</summary>
        public static readonly Field Enabled = FindByName("Enabled");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUser = FindByName("UpdateUser");

        /// <summary>更新者</summary>
        public static readonly Field UpdateUserID = FindByName("UpdateUserID");

        /// <summary>更新时间</summary>
        public static readonly Field UpdateTime = FindByName("UpdateTime");

        /// <summary>更新地址</summary>
        public static readonly Field UpdateIP = FindByName("UpdateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得商品阶梯价格表字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>商品ID</summary>
        public const String GoodsId = "GoodsId";

        /// <summary>商品公共表ID</summary>
        public const String GoodsCommonId = "GoodsCommonId";

        /// <summary>店铺ID</summary>
        public const String StoreId = "StoreId";

        /// <summary>商品SkuID</summary>
        public const String SkuId = "SkuId";

        /// <summary>最小购买数量</summary>
        public const String MinQuantity = "MinQuantity";

        /// <summary>最大购买数量</summary>
        public const String MaxQuantity = "MaxQuantity";

        /// <summary>原价</summary>
        public const String OriginalPrice = "OriginalPrice";

        /// <summary>销售价</summary>
        public const String Price = "Price";

        /// <summary>是否启用</summary>
        public const String Enabled = "Enabled";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";

        /// <summary>更新者</summary>
        public const String UpdateUser = "UpdateUser";

        /// <summary>更新者</summary>
        public const String UpdateUserID = "UpdateUserID";

        /// <summary>更新时间</summary>
        public const String UpdateTime = "UpdateTime";

        /// <summary>更新地址</summary>
        public const String UpdateIP = "UpdateIP";
    }
    #endregion
}
