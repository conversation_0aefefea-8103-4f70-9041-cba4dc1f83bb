﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using B2B2CShop.Common;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using Pek;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace B2B2CShop.Entity;

public partial class GoodsTieredPrice : DHEntityBase<GoodsTieredPrice>
{
    #region 对象操作
    static GoodsTieredPrice()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(GoodsId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(GoodsId), nameof(MinQuantity));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化GoodsTieredPrice[商品阶梯价格表]数据……");

    //    var entity = new GoodsTieredPrice();
    //    entity.GoodsId = 0;
    //    entity.GoodsCommonId = 0;
    //    entity.StoreId = 0;
    //    entity.MinQuantity = 0;
    //    entity.MaxQuantity = 0;
    //    entity.OriginalPrice = 0.0;
    //    entity.Price = 0.0;
    //    entity.Enabled = true;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化GoodsTieredPrice[商品阶梯价格表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>商品ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Goods? Goods => Extends.Get(nameof(Goods), k => Goods.FindById(GoodsId));

    /// <summary>商品ID</summary>
    [Map(nameof(GoodsId), typeof(Goods), "Id")]
    public String? GoodsName => Goods?.Name;
    /// <summary>商品公共表ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public GoodsCommon? GoodsCommon => Extends.Get(nameof(GoodsCommon), k => GoodsCommon.FindById(GoodsCommonId));

    /// <summary>商品公共表ID</summary>
    [Map(nameof(GoodsCommonId), typeof(GoodsCommon), "Id")]
    public String? GoodsCommonName => GoodsCommon?.Name;
    /// <summary>店铺ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Store? Store => Extends.Get(nameof(Store), k => Store.FindById(StoreId));

    /// <summary>店铺ID</summary>
    [Map(nameof(StoreId), typeof(Store), "Id")]
    public String? StoreName => Store?.Name;
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="goodsId">商品ID</param>
    /// <param name="goodsCommonId">商品公共表ID</param>
    /// <param name="storeId">店铺ID</param>
    /// <param name="minQuantity">最小购买数量</param>
    /// <param name="enabled">是否启用</param>
    /// <param name="start">更新时间开始</param>
    /// <param name="end">更新时间结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<GoodsTieredPrice> Search(Int64 goodsId, Int64 goodsCommonId, Int64 storeId, Int32 minQuantity, Boolean? enabled, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (goodsId >= 0) exp &= _.GoodsId == goodsId;
        if (goodsCommonId >= 0) exp &= _.GoodsCommonId == goodsCommonId;
        if (storeId >= 0) exp &= _.StoreId == storeId;
        if (minQuantity >= 0) exp &= _.MinQuantity == minQuantity;
        if (enabled != null) exp &= _.Enabled == enabled;
        exp &= _.UpdateTime.Between(start, end);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    //根据goodsid和购买数量获取价格
    public static GoodsTieredPrice GetPriceByGoodsIdAndQuantity(Int64 goodsId, Int32 quantity)
    {
        var model = Find(_.GoodsId == goodsId & _.MinQuantity <= quantity & _.MaxQuantity >= quantity);
        if (model == null) model = Find(_.GoodsId == goodsId & _.MinQuantity <= quantity & _.MaxQuantity == 0);
        return model;
    }
    public static List<GoodsTieredPrice> GetRatePriceByGoodsId(long goodsId,decimal exchangeRate)
    {
        IList<GoodsTieredPrice> tieList = new List<GoodsTieredPrice>();
        if (Meta.Session.Count < 1000) tieList = Meta.Cache.FindAll(e => e.GoodsId == goodsId);
        tieList = FindAll(_.GoodsId == goodsId);
        var curTieredPrice = new List<GoodsTieredPrice> { };

        // 转换货币价格
        foreach (var tier in tieList)
        {
            var tierTemp = tier.CloneEntity();
            tierTemp.Price = (tierTemp.Price*exchangeRate).ToKeepTwoDecimal();
            tierTemp.OriginalPrice = (tierTemp.OriginalPrice*exchangeRate).ToKeepTwoDecimal();
            curTieredPrice.Add(tierTemp);
        }
        return curTieredPrice;
    }

    /// <summary>
    /// 获取商品默认的阶梯价格列表
    /// </summary>
    /// <param name="goodsId"></param>
    /// <returns></returns>
    public static IList<GoodsTieredPrice> FindAllByGoodsIdAndSkuId(Int64 goodsId,Int64 skuId)
    {
        if (goodsId < 0) return [];
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.SkuId == skuId);

        return FindAll(_.GoodsId == goodsId & _.SkuId == skuId);
    }

    // Select Count(Id) as Id,Category From DH_GoodsTieredPrice Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<GoodsTieredPrice> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IGoodsTieredPrice ToModel()
    {
        var model = new GoodsTieredPrice();
        model.Copy(this);

        return model;
    }
    public static void DeleteByGIds(string Ids)
    {
        Delete(_.GoodsId.In(Ids.Split(",")));
        Meta.Cache.Clear("", true);
    }
    public static void DeleteBySkuIds(string Ids)
    {
        Delete(_.SkuId.In(Ids.Split(",")));
        Meta.Cache.Clear("", true);
    }
    #endregion
}
