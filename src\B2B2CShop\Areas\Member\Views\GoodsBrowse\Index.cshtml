﻿@using B2B2CShop.Dto
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/orders.css");

    var site = SiteInfo.GetDefaultSeo();
}
<style>
    .layui-laypage a,
    .layui-laypage span {
        font-size: 14px;
    }

    .main {
        margin-left: 15%;
        width: 70%;
    }

    .row-display {
        display: none;
    }
</style>
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
        @*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "GoodsBrowse")">@T("浏览历史")</a>
    </div>
    <!-- 用户中心 -->
    <div class="userInfoBox">
      
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            <a href="@Url.Action("Index", "Wish", new { area = "Member" })">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
            @*             <a href="@Url.Action("Index", "Message", new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate", new { area = "Member" })">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice", new { area = "Member" })">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact", new { area = "Member" })">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
            @*             <a href="@Url.Action("Index", "PaymentMethod", new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content" data-show="true">
            <form method="get">
                    <div class="flex" style="margin-right: 0.5vw; margin-bottom:0.5vw">
                        <div class="layui-form">
                            <div class="layui-inline" id="ID-laydate-rangeLinked">
                                <div class="layui-form-mid" style="white-space: nowrap;">@T("查询时间"):</div>
                                <div class="layui-input-inline">
                                    <input id="ID-laydate-start-date-1" class="layui-input" placeholder="@T("开始日期")" lay-filter="startTime" name="startTime" value="@Model.startTime">
                                </div>
                                -
                                <div class="layui-input-inline">
                                    <input id="ID-laydate-end-date-1" class="layui-input" placeholder="@T("结束日期")" lay-filter="endTime" name="endTime" value="@Model.endTime">
                                </div>
                            </div>
                            <script>
                                var laydate = layui.laydate;
                                // 日期范围 - 左右面板联动选择模式
                                laydate.render({
                                    elem: '#ID-laydate-rangeLinked',
                                    range: ['#ID-laydate-start-date-1', '#ID-laydate-end-date-1'],
                                    lang: '@Language?.UniqueSeoCode',
                                    rangeLinked: true, // 开启日期范围选择时的区间联动标注模式 ---  2.8+ 新增
                                    done: function(value, date, endDate){
                                        // 选择日期后自动提交表单
                                        $('#ID-laydate-rangeLinked').closest('form').submit();
                                    }
                                });
                            </script>
                        </div>
                        <div style="width: 80px;margin: auto;">
                            <button class="button button_blue" type="submit">@T("查询")</button>
                        </div>
                    </div>
            </form>

            <div class="tablesBox">
                <table class="layui-table" lay-skin="line" style="background-color: white;margin: 0px;">
                    <colgroup>
                        <col width="50%">
                        <col width="15%">
                    </colgroup>
                    <tbody>
                        @foreach (GoodsBrowseDto item in Model.BrowseList)
                        {
                            if (item.GoodsName.IsNullOrEmpty())
                            {
                                continue;
                            }
                            <tr>
                                <td>
                                    <div class="goodsInfo" style="place-items: center;">
                                        <div class="" style="width: 30%;flex-direction: column;min-width: fit-content;">
                                            <a href="/Goods/Index?GoodsId=@item.GoodsId">
                                                <img src="/public/images/icons/hlk.png" data-src="@item.GoodsImage" class="lazy-load" alt="@T("商品图片")" style="width: 100%;margin-top: auto;">
                                            </a>
                                        </div>
                                        <div class="goodsInformation"
                                             style="width: 60%;margin-left: 5%;margin-right: auto;">
                                            <div class="tdTitle"> <span>@item.GoodsName</span> </div>
                                            <div lass="tdTitle">
                                                    @T("单价"): @<EMAIL>("F2")
                                            </div>
                                            <div>@T("浏览日期")：<span class="textOver">@item.CreateTime.ToString("yyyy-MM-dd HH:mm:ss")</span></div>
                                        </div>
                                    </div>
                                </td>
                                <!-- Replace the existing static tiered price section with the dynamic one -->
                                <td class="fnTd">
                                    <!-- 显示单个价格 -->

                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
                <div id="pagingBox" style="text-align: right;"></div>
                <script>
                    layui.use(function () {
                        var laypage = layui.laypage;
                        laypage.render({
                            elem: 'pagingBox',
                            count: @Model.pages.TotalCount, // 数据总数
                            limit: @(Model.pages.PageSize> 0 ? Model.pages.PageSize : 10), // 每页显示条数
                            limits: [5,10, 20, 50, 100], // 每页条数的选择项
                            curr: @(Model.pages.PageIndex > 0 ? Model.pages.PageIndex : 1), // 当前页码
                            groups: 5, // 连续显示页码个数
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 自定义布局
                            theme: '#2C79E8', // 自定义主题色
                            prev: '@T("上一页")',
                            next: '@T("下一页")',
                            first: '@T("首页")',
                            last: '@T("尾页")',
                            countText: ['@T("共") ',' @T("条")'],
                            skipText: ['@T("到第")', '@T("页")', '@T("确认")'],
                            limitTemplet: function(item) {
                              return item + ' @T("条/页")';
                            },
                            jump: function(obj, first) {
                                // 首次不执行（首次加载时不跳转）
                                if (!first) {
                                    // 创建一个隐藏的表单来提交所有参数
                                    var $form = $('<form></form>');
                                    $form.attr('action', '@Url.Action("Index")');
                                    $form.attr('method', 'get');
                                    $form.css('display', 'none');

                                    // 添加页码参数
                                    $form.append('<input type="hidden" name="page" value="' + obj.curr + '" />');

                                    // 添加每页条数参数
                                    $form.append('<input type="hidden" name="limit" value="' + obj.limit + '" />');

                                    // 获取当前表单中的所有参数
                                    $('form:first input, form:first select').each(function() {
                                        var name = $(this).attr('name');
                                        var value = $(this).val();

                                        // 如果存在名称和值，并且不是页码相关参数，则添加到隐藏表单中
                                        if (name && value && name !== 'page' && name !== 'limit') {
                                            $form.append('<input type="hidden" name="' + name + '" value="' + value + '" />');
                                        }
                                    });

                                    // 将表单添加到文档中并提交
                                    $('body').append($form);
                                    $form.submit();
                                }
                            }
                        });
                    });
                </script>
            </div>
        </div>
    </div>
</div>
<script src="/public/script/lazyload.js"></script>
<script asp-location="Footer">
    window.addEventListener('load', function() {
        window.lazyLoadImages();
    });

</script>