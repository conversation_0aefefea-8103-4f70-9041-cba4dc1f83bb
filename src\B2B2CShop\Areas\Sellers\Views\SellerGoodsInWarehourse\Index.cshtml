﻿@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewBag.LeftMenu = "Goods";
    ViewBag.LeftChileMenu = "GoodsInWarehourse";

    // 页面样式
    PekHtml.AppendPageCssClassParts("html-sellergoodsonline-page");
}
@await Html.PartialAsync("_Left")

<div class="seller_right">
    <div class="seller_items">
        <ul>
            <li class="@(Model.state==0?"current":"")"><a href="@Url.Action("Index",new{state="0"})">@T("仓库中的商品")</a></li>
            <li class="@(Model.state==10?"current":"")"><a href="@Url.Action("Index",new{state="10"})">@T("违规的商品")</a></li>
            @* <li><a href="">@T("待审核的商品")</a></li> *@
        </ul>
    </div>
    <div class="p20">
        <form method="get" action="">
            <input type="hidden" name="state" value="@Model.state" />
            <table class="search-form">
                <tr>
                    <td>&nbsp;</td>
                    @* <th>@T("本店分类")</th>
                    <td class="w160">
                        <select name="storegc_id" class="w150">
                            <option value="0">@T("-请选择-")</option>
                        </select>
                    </td> *@
                    <th>
                        <select name="search_type">
                            <option value="0" selected="@(Model.search_type == 0)">@T("商品名称")</option>
                            <option value="1" selected="@(Model.search_type == 1)">@T("商家货号")</option>
                            <option value="2" selected="@(Model.search_type == 2)">@T("平台货号")</option>
                        </select>
                    </th>
                    <td class="w160"><input type="text" class="text" name="keyword" value="@Model.keyword" /></td>
                    <td class="tc w70">
                        <input type="submit" class="submit" value="@T("搜索")" />
                    </td>
                </tr>
            </table>
        </form>
        <table class="dssc-default-table">
            <thead>
                <tr ds_type="table_header">
                    <th class="w100"></th>
                    <th class="w50"></th>
                    <th>@T("商品名称")</th>
                    <th class="w200">@T("价格")</th>
                    <th class="w300">@T("上架")</th>
                    <th class="w200">@T("操作")</th>
                </tr>
                <tr>
                    <td class="tc"><input type="checkbox" id="all" class="checkall" /></td>
                    <td colspan="10">
                        <label for="all">@T("全选")</label>
                        <a href="javascript:void(0);" class="dssc-btn-mini" ds_type="batchbutton" uri="@Url.Action("Delete","SellerGoodsOnline")" name="ids" confirm="@T("您确定要删除吗?")"><i class="iconfont">&#xe725;</i>@T("删除")</a>
                        <a href="javascript:void(0);" class="dssc-btn-mini" name="ids" ds_type="batchbutton" uri="@Url.Action("ShelfGoods")" confirm="@T("您确定要上架吗?")">@T("上架")</a>
                    </td>
                </tr>
            </thead>
            <tbody>
                @{
                    foreach (Goods product in Model.list)
                    {
                        var goodsIamgePath = AlbumPic.FindByName(product.GoodsImage??"")?.Cover ?? "";

                        <tr>
                            <th class="tc"><input type="checkbox" class="checkitem tc" value="@product.Id" /></th>
                            <th colspan="20">@T("平台货号")：@product.Id</th>
                        </tr>
                        <tr>
                            <td class="trigger">
                                @* <i class="tip iconfont" dstype="ajaxGoodsList" data-comminid="@product.Id" title="@T("点击展开查看此商品全部规格；规格值过多时请横向拖动区域内的滚动条进行浏览。")">&#xe6db;</i> *@
                            </td>
                            <td>
                                <div class="pic-thumb">
                                    <a href="@Url.Action("Index","Goods",new {Area = "" ,id = product.Id})" target="_blank"><img src="@goodsIamgePath" /></a>
                                </div>
                            </td>
                            <td class="tl">
                                <dl class="goods-name">
                                    <dt style="max-width: 450px !important;">
                                        <a href="@Url.Action("Index","Goods",new {Area = "" ,id = product.Id})" target="_blank">@product.Name</a>
                                    </dt>
                                    <dd>@T("商家货号")：</dd>
                                    <dd class="serve">
                                        @if (product.GoodsCommend == 1)
                                        {
                                            <span class="open" title="@T("店铺推荐商品")"><i class="commend">@T("荐")</i></span>
                                        }
                                        <span class="" title="@T("手机端商品详情")"><i class="iconfont">&#xe601;</i></span> <span class="" title="@T("商品页面二维码")">
                                            <i class="iconfont">&#xe72d;</i>
                                            <div class="QRcode">
                                                <a target="_blank" href="@goodsIamgePath">@T("下载标签")</a>
                                                <p><img src="@goodsIamgePath" /></p>
                                            </div>
                                        </span>
                                    </dd>
                                </dl>
                            </td>
                            <td><span>$ @product.GoodsPrice</span></td>
                            <td><a href="javascript:void(0)" class="dssc-btn" onclick="ds_ajaxget_confirm('@Url.Action("ShelfGoods", new { ids = product.Id.SafeString() })', '@T("您确定要上架该商品吗?")')" >@T("上架")</a></td>
                            <td class="dscs-table-handle">
                                <span>
                                    <a href="@Url.Action("EditGoodsDetail","SellerGoodsOnline",new{id = product.Id})" class="btn-blue">
                                        <i class="iconfont">&#xe731;</i>
                                        <p>@T("编辑")</p>
                                    </a>
                                </span>
                                <span>
                                    <a href="javascript:void(0)" onclick="ds_ajaxget_confirm('@(Url.Action("Delete","SellerGoodsOnline", new { ids = product.Id.SafeString() }))', '@T("您确定要删除吗?")')" class="btn-red">
                                        <i class="iconfont">&#xe725;</i>
                                        <p>@T("删除")</p>
                                    </a>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th class="tc" colspan="20"></th>
                        </tr>
                    }
                }
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="7">
                        <div class="pagination">
                            <ul class="pagination">
                                @Html.Raw(Model.PageHtml)
                            </ul>
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
        <script src="/static/plugins/jquery.poshytip.min.js"></script>
        <script src="/static/home/<USER>/store_goods_list.js"></script>
        <script>
            $(function(){
                //Ajax提示
                $('.tip').poshytip({
                    className: 'tip-yellowsimple',
                    showTimeout: 1,
                    alignTo: 'target',
                    alignX: 'center',
                    alignY: 'top',
                    offsetY: 5,
                    allowTipHover: false
                });
            });
        </script>


    </div>
</div>
   