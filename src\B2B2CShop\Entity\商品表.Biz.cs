﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using B2B2CShop.Common;
using B2B2CShop.Dto;
using DH.Entity;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Serialization;
using NewLife.Threading;
using NewLife.Web;
using NuGet.Packaging.Signing;
using Pek;
using Pek.Timing;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace B2B2CShop.Entity;

public partial class Goods : DHEntityBase<Goods>
{
    #region 对象操作
    static Goods()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(GoodsCommonId));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 这里验证参数范围，建议抛出参数异常，指定参数名，前端用户界面可以捕获参数异常并聚焦到对应的参数输入框
        if (Name.IsNullOrEmpty()) throw new ArgumentNullException(nameof(Name), "商品名称不能为空！");

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化Goods[商品表]数据……");

    //    var entity = new Goods();
    //    entity.Id = 0;
    //    entity.GoodsCommonId = 0;
    //    entity.Name = "abc";
    //    entity.AdvWord = "abc";
    //    entity.StoreId = 0;
    //    entity.StoreName = "abc";
    //    entity.CId = 0;
    //    entity.Cid1 = 0;
    //    entity.Cid2 = 0;
    //    entity.Cid3 = 0;
    //    entity.BrandId = 0;
    //    entity.GoodsPrice = 0.0;
    //    entity.GoodsPromotionPrice = 0.0;
    //    entity.GoodsPromotionType = 0.0;
    //    entity.GoodsMarketPrice = 0.0;
    //    entity.GoodsSerial = "abc";
    //    entity.GoodsStorageAlarm = 0;
    //    entity.GoodsClick = 0;
    //    entity.GoodsSalenum = 0;
    //    entity.GoodsCollect = 0;
    //    entity.GoodsSpec = 0;
    //    entity.MerchantMaterial = 0;
    //    entity.GoodsStorage = 0;
    //    entity.GoodsWeight = 0.0;
    //    entity.GoodsImage = "abc";
    //    entity.Content = "abc";
    //    entity.MobileContent = "abc";
    //    entity.GoodsLock = 0;
    //    entity.GoodsState = 0;
    //    entity.GoodsVerify = 0;
    //    entity.GoodsAddTime = 0;
    //    entity.GoodsEditTime = 0;
    //    entity.AreaId1 = 0;
    //    entity.AreaId2 = 0;
    //    entity.RegionId = 0;
    //    entity.ColorId = "abc";
    //    entity.TransportId = 0;
    //    entity.GoodsFreight = 0.0;
    //    entity.GoodsVat = 0;
    //    entity.GoodsCommend = 0;
    //    entity.GoodsStcids = "abc";
    //    entity.EvaluationGoodStar = "abc";
    //    entity.EvaluationCount = "abc";
    //    entity.IsVirtual = 0;
    //    entity.VirtualIndate = 0;
    //    entity.VirtualLimit = 0;
    //    entity.VirtualInvalidRefund = 0;
    //    entity.VirtualContent = "abc";
    //    entity.IsGoodsFCode = 0;
    //    entity.IsAppoint = 0;
    //    entity.IsHaveGift = "abc";
    //    entity.IsPlatformStore = 0;
    //    entity.GoodsMGDiscount = "abc";
    //    entity.GoodsSort = 0;
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化Goods[商品表]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>商品公共表ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public GoodsCommon? GoodsCommon => Extends.Get(nameof(GoodsCommon), k => GoodsCommon.FindById(GoodsCommonId));

    /// <summary>商品公共表ID</summary>
    [Map(nameof(GoodsCommonId), typeof(GoodsCommon), "Id")]
    public String? GoodsCommonName => GoodsCommon?.Name;
    /// <summary>店铺ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Store? Store => Extends.Get(nameof(Store), k => Store.FindById(StoreId));
    /// <summary>商品品牌ID</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Brand? Brand => Extends.Get(nameof(Brand), k => Brand.FindById(BrandId));

    /// <summary>商品品牌ID</summary>
    [Map(nameof(BrandId), typeof(Brand), "Id")]
    public String? BrandName => Brand?.Name;

    /// <summary>商品售卖区域id</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public Transport? Transport => Extends.Get(nameof(Transport), k => Transport.FindById(TransportId));
    /// <summary>物料对象</summary>
    [XmlIgnore, IgnoreDataMember, ScriptIgnore]
    public MerchantMaterial? Material => Entity.MerchantMaterial.FindById(MerchantMaterial);
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="goodsCommonId">商品公共表ID</param>
    /// <param name="storeId">店铺ID</param>
    /// <param name="cId">商品分类ID</param>
    /// <param name="cid1">一级分类ID</param>
    /// <param name="cid2">二级分类ID</param>
    /// <param name="cid3">三级分类ID</param>
    /// <param name="brandId">商品品牌ID</param>
    /// <param name="start">编号开始</param>
    /// <param name="end">编号结束</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<Goods> Search(Int64 goodsCommonId, Int64 storeId, Int64 cId, Int64 cid1, Int64 cid2, Int64 cid3, Int32 brandId, DateTime start, DateTime end, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (goodsCommonId >= 0) exp &= _.GoodsCommonId == goodsCommonId;
        if (storeId >= 0) exp &= _.StoreId == storeId;
        if (cId >= 0) exp &= _.CId == cId;
        if (cid1 >= 0) exp &= _.Cid1 == cid1;
        if (cid2 >= 0) exp &= _.Cid2 == cid2;
        if (cid3 >= 0) exp &= _.Cid3 == cid3;
        if (brandId >= 0) exp &= _.BrandId == brandId;
        exp &= _.Id.Between(start, end, Meta.Factory.Snow);
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }
    /// <summary>
    /// 根据商品公共ID和店铺Id获取商品对象
    /// </summary>
    /// <param name="cId"></param>
    /// <param name="sId"></param>
    /// <returns></returns>
    public static Goods? FindByCIdAndSId(Int64 cId, Int64 sId)
    {
        if (cId.IsNull()) return null;
        if (sId.IsNull()) return null;
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.GoodsCommonId == cId && e.StoreId == sId);

        return Find(_.GoodsCommonId == cId & _.StoreId == sId);
    }
    public static IList<Goods> Search(int state, Int64 sId, string keys)
    {
        WhereExpression where = new WhereExpression();
        where &= _.GoodsState == state;
        keys = keys ?? "";
        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.GoodsState == state && e.StoreId == sId && e.Name.Contains(keys));

        return FindAll(_.GoodsState == state & _.StoreId == sId & _.Name.Contains(keys));
    }
    /// <summary>
    /// 根据商品ID集合获取数据
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public static IList<Goods> FindAllByIds(string ids)
    {
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => ids.Split(',').Contains(e.Id.SafeString()));

        return FindAll(_.Id.In(ids.Trim(',')));
    }

    /// <summary>根据商品名称查找</summary>
    /// <param name="goodsName">商品名称</param>
    /// <returns>实体列表</returns>
    public static IList<Goods> FindByGoodsNameAndUserId(string goodsName, int userId,int state, PageParameter page)
    {
        if (Meta.Session.Count < 1000)
        {
            var List = FindAllWithCache();
            List = List.Where(e => e.CreateUserID.Equals(userId) && e.GoodsState == state).ToList();
            if (!goodsName.IsNullOrWhiteSpace())
            {
                List = List.Where(e => e.Name?.Contains(goodsName) == true).ToList();
            }
            page.TotalCount = List.Count;
            return List.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
        }

        var exp = new WhereExpression();
        exp &= _.CreateUserID == userId;
        exp &= _.GoodsState == state;
        if (!goodsName.IsNullOrWhiteSpace()) exp &= _.Name.Contains(goodsName);
        return FindAll(exp, page);
    }
    /// <summary>根据商家货号查找</summary>
    /// <param name="goodsSerial">商家货号</param>
    /// <returns>实体列表</returns>
    public static IList<Goods> FindByGoodsSerialAndUserId(string goodsSerial, int userId, int state, PageParameter page)
    {
        if (Meta.Session.Count < 1000)
        {
            var List = FindAllWithCache();
            List = List.Where(e => e.CreateUserID.Equals(userId)&&e.GoodsState == state).ToList();
            if (!goodsSerial.IsNullOrWhiteSpace())
            {
                List = List.Where(e => e.GoodsSerial?.Contains(goodsSerial) == true).ToList();
            }
            page.TotalCount = List.Count;
            return List.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
        }

        var exp = new WhereExpression();
        exp &= _.CreateUserID == userId;
        exp &= _.GoodsState == state;
        if (!goodsSerial.IsNullOrWhiteSpace()) exp &= _.GoodsSerial.Contains(goodsSerial);
        return FindAll(exp, page);
    }
    /// <summary>根据商品ID查找</summary>
    public static IList<Goods> FindByIdAndUserId(string goodsId, int userId, int state, PageParameter page)
    {
        if (Meta.Session.Count < 1000)
        {
            var List = FindAllWithCache();
            List = List.Where(e => e.CreateUserID.Equals(userId) && e.GoodsState == state).ToList();
            if (!goodsId.IsNullOrWhiteSpace())
            {
                List = List.Where(e => e.Id.SafeString() == goodsId).ToList();
            }
            page.TotalCount = List.Count;
            return List.Skip((page.PageIndex - 1) * page.PageSize).Take(page.PageSize).ToList();
        }
        var exp = new WhereExpression();
        exp &= _.CreateUserID == userId;
        exp &= _.GoodsState == state;
        if (!goodsId.IsNullOrWhiteSpace()) exp &= _.Id == goodsId;
        return FindAll(exp, page);
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="CId">二级商品分类</param>
    /// <param name="Inventory">库存量是否大于0</param>
    /// <param name="OnSaleint">是否在售商品</param>
    /// <param name="RoSH">是否符合RoSH</param>
    /// <param name="NewGoods">是否新商品</param>
    /// <param name="JsonKey">分类属性</param>
    /// <param name="Key">关键字</param>
    /// <param name="LId">语言</param>
    /// <param name="UserId">语言</param>
    /// <param name="ExchangeRate">语言</param>
    /// <param name="StoreId">语言</param>
    /// <param name="page"></param>
    /// <returns></returns>
    public static List<GoodsDto> SearchLan(Int64 CId,Boolean Inventory, Boolean OnSaleint, Boolean RoSH, Boolean NewGoods,String JsonKey,String Key, Int32 LId,Int64 UserId,Decimal ExchangeRate,Int64 StoreId,  PageParameter page, Int32 GoodsState = -1)
    {
        WhereExpression exp = new WhereExpression();

        if (CId > 0) exp &= _.Cid2 == CId;
        if (StoreId > 0) exp &= _.StoreId == StoreId;
        // 如果 Inventory 为 true，添加库存大于0的条件
        if (Inventory)
        {
            // 获取所有库存大于0的物料ID
            var materialIds = Entity.MerchantMaterial.FindAllInStock()
                .Select(m => m.Id)
                .ToList();

            // 如果有库存大于0的物料，添加条件
            if (materialIds.Count > 0)
            {
                exp &= _.MerchantMaterial.In(materialIds);
            }
            else
            {
                // 如果没有库存大于0的物料，返回空列表
                return new List<GoodsDto>();
            }
        }

        if (OnSaleint) exp &= _.GoodsState == 1;
        if (NewGoods)
        {
            var dt1 = UnixTime.ToTimestamp();
            var dt2 = UnixTime.ToTimestamp(DateTime.Now.AddDays(-7));
            exp &= _.GoodsAddTime >= dt2 & _.GoodsAddTime <= dt1;
        }
        if (Key.IsNotNullOrWhiteSpace())
        {
            //exp &= _.Name.Contains(Key);
            var goodsLans = GoodsLan.FindAll(GoodsLan._.LId == LId);
            var glanlist = goodsLans.Where(e=>!e.LanName.IsNullOrWhiteSpace()).Where(e => e.LanName.Contains(Key) || e.LanName.Contains(Key.ToUpper())).ToList();
            if (glanlist.Count() > 0)
            {
                var gIds = glanlist.Select(e => e.GId).ToList();
                exp &= _.Id.In(gIds);
            }
            else
            {
                exp &= _.Id==0;
            }
        }
        if (JsonKey.IsNotNullOrWhiteSpace())
        {
            var arrs = JsonKey.ToJsonEntity<List<CommonDto>>();
            if (arrs!=null)
            {
                foreach (var item in arrs)
                {
                    if (!string.IsNullOrWhiteSpace(item.id) && !string.IsNullOrWhiteSpace(item.value))
                    {
                        var gIds = GoodsExAttributes.FindAllByClassIdAndField(CId, item.id, item.value).Select(e => e.GoodsId).ToList();
                        if (gIds.Count == 0)
                        {
                            return new List<GoodsDto>();
                        }
                        exp &= _.Id.In(gIds);
                    }
                }
            }
        };
        if (GoodsState >= 0) exp &= _.GoodsState == GoodsState;

        // 直接使用数据库查询的结果
        var list = FindAll(exp, page).Select(e => new GoodsDto
        {
            Id = e.Id.SafeString(),
            CId = e.CId,
            CId1 = e.Cid1,
            CId2 = e.Cid2,
            CId3 = e.Cid3,
            Name = GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanName ?? e.Name,
            GoodsStorage = Entity.MerchantMaterial.GetQuantityByWIds(e.MaterialIds),
            AdvWord = e.AdvWord,
            GoodsImage = AlbumPic.FindByNameAndSId(GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanGoodsImage ?? e.GoodsImage ?? "", e.StoreId)?.Cover,
            GoodsPrice = ((e.Material?.Price??e.GoodsPrice) * ExchangeRate).ToKeepTwoDecimal(),
            IsWish = Wishlist.FindByUIdAndGoddsId(UserId, e.Id) != null,
            GoodsSalenum = e.GoodsSalenum,
            GoodsBuynum = e.GoodsBuynum,
            EvaluationGoodStar = e.EvaluationGoodStar,
            WareHouseMaterials = WareHouseMaterial.FindAllByMaterialIdsLan(e.MaterialIds,LId),
            MaterialTieredPrices = MaterialTieredPrice.GetRatePriceByMaterialId(e.MerchantMaterial, ExchangeRate),
            GoodsClassName = (GoodsClassLan.FindByGIdAndLId(e.Cid2,LId)?.Name).IsNullOrWhiteSpace() ? GoodsClass.FindById(e.Cid2)?.Name : GoodsClassLan.FindByGIdAndLId(e.Cid2, LId)?.Name,
            CompanyName = e.Store?.CompanyName ?? "",
            MaterialId = e.MerchantMaterial,
            MaterialIds = e.MaterialIds.Split(',',StringSplitOptions.RemoveEmptyEntries).ToList(),
            DeliveryTime = GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanDeliveryTime ?? e.DeliveryTime,
            DeliveryDate = GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanDeliveryDate ?? e.DeliveryDate,
        }).ToList();
        return list;
    }

    /// <summary>
    /// 获取随机数量的商品
    /// </summary>
    /// <param name="RandomNum"></param>
    /// <param name="LId"></param>
    /// <param name="ExchangeRate"></param>
    /// <returns></returns>
    public static List<GoodsDto> FindAllByRandomLan(Int32 RandomNum,Int32 LId,Decimal ExchangeRate)
    {
        var goodsList = FindAll();
        List<Goods> likeList = new();
        if (goodsList.Count > 0)
        {
            for (int i = 0; i < RandomNum; i++)
            {
                Random random = new Random();
                int randomNumber = random.Next(0, goodsList.Count);
                var modal = goodsList[randomNumber];
                likeList.Add(modal);
            }
        }
        var list = likeList.Select(e => new GoodsDto
        {
            Id = e.Id.SafeString(),
            Name = GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanName ?? e.Name,
            GoodsPrice = (e.Material?.Price??e.GoodsPrice * ExchangeRate).ToKeepTwoDecimal(),
            GoodsStorage = B2B2CShop.Entity.MerchantMaterial.GetQuantityByWIds(e.MaterialIds ?? ""),
            GoodsImage = AlbumPic.FindByNameAndSId(GoodsLan.FindByGIdAndLId(e.Id, LId)?.LanGoodsImage ?? e.GoodsImage ?? "", e.StoreId)?.Cover,
            GoodsSalenum = e.GoodsSalenum,
            GoodsBuynum = e.GoodsBuynum,
            EvaluationGoodStar = e.EvaluationGoodStar,
            GoodsSKUDetail = GoodsSKUDetail.FindAllByGoodsId(e.Id)
        }).ToList();
        return list;
    }
    public static IList<Goods> Search(long cId,long storeId,string key)
    {
        if (cId == 0) return [];
        var exp = new WhereExpression();
        exp &= _.CId == cId;
        if (storeId != 0) exp &= _.StoreId == storeId;
        if (!key.IsNullOrEmpty()) exp &= _.Name.Contains(key);
        return FindAll(exp);
    }
    // Select Count(Id) as Id,Category From DH_Goods Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<Goods> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IGoods ToModel()
    {
        var model = new Goods();
        model.Copy(this);

        return model;
    }

    /// <summary>
    /// 修改商品的销量和购买人数
    /// </summary>
    /// <param name="Id"></param>
    /// <param name="Num"></param>
    public static void UpdateByGoodsSalenum(Int64 Id,Int32 Num)
    {
        var goods = FindById(Id);
        if (goods == null) return;
        goods.GoodsBuynum = goods.GoodsBuynum + 1;
        goods.GoodsSalenum = goods.GoodsSalenum + Num;
        goods.Update();
    }

    public static void DelByCommonIds(string Ids)
    {
        if (Entity<Goods>.Delete(@_.GoodsCommonId.In(Ids.Trim(','))) > 0)
        {
            Entity<Goods>.Meta.Cache.Clear("");
            Meta.Cache.Clear("", true);
        }
    }

    public static void UnshowByCommonIds(string Ids)
    {
        var list = FindAll(_.GoodsCommonId.In(Ids.Trim(',')));
        foreach (var item in list)
        {
            item.GoodsState = 0;
            item.Update();
        }
    }
    public static void ShelfByCommonIds(string Ids)
    {
        var list = FindAll(_.GoodsCommonId.In(Ids.Trim(',')));
        foreach (var item in list)
        {
            item.GoodsState = 1;
            item.Update();
        }
    }
    /// <summary>根据商品公共表ID查找</summary>
    /// <param name="goodsCommonId">商品公共表ID</param>
    /// <returns>实体列表</returns>
    public static Goods FindByGoodsCommonId(Int64 goodsCommonId)
    {
        if (goodsCommonId < 0) return null;

        return Find(_.GoodsCommonId == goodsCommonId);
    }

    public static IList<Goods> FindAllByName(string name)
    {
        if (Meta.Session.Count<1000)
        {
            return Meta.Cache.FindAll(e => e.Name.Contains(name) && e.GoodsState == 1);
        }
        return FindAll(_.Name.Contains(name) & _.GoodsState == 1);
    }

    /// <summary>
    /// 修改商品评分
    /// </summary>
    /// <param name="GoodsId"></param>
    public static void UpdateByEvaluationGoodStar(Int64 GoodsId)
    {
        var modelGoods = FindById(GoodsId);
        if (modelGoods == null) return;
        var listEvaluateGoods = EvaluateGoods.FindAllByGoodsId(modelGoods.Id);
        if(listEvaluateGoods.Count() > 0)
        {
            var score = listEvaluateGoods.Average(e => e.GoodsScore);
            modelGoods.EvaluationGoodStar = (Math.Round(score / (double)2, 1)).SafeString();
            modelGoods.EvaluationCount = ((modelGoods.EvaluationCount ?? "0").ToInt() + 1).SafeString();
            modelGoods.Update();
        }
    }
    #endregion
}