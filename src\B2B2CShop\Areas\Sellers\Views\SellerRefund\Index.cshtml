@using B2B2CShop.Entity
@using Pek.Timing
@inject IWorkContext workContext
@*
    退款管理页面
    商家处理买家退款申请
*@
@{
    ViewBag.LeftMenu = "Orders";
    ViewBag.LeftChileMenu = "Refund";
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-sellerorder-page");
    var lid = workContext.WorkingLanguage.Id;
}
@await Html.PartialAsync("_Left")
<div class="seller_right">
    <div class="seller_items">
        <ul>
            <li class="@(Model.type==1?"current":"")"><a href="@Url.Action("Index",new {type = 1})">@T("退款")</a></li>
            <li class="@(Model.type==2?"current":"")"><a href="@Url.Action("Index",new {type = 2})">@T("退货")</a></li>
        </ul>
    </div>
    <div class="p20">
        <form method="get" action="">
            <input type="hidden" name="type" value="@Model.type" />
            <table class="search-form">
                <tr>
                    <td>&nbsp;</td>
                    <th>@T("申请时间")</th>
                    <td class="w240">
                        <input name="applyStartTime" id="applyStartTime" type="text" class="text w70" value="@(Model.applyStartTime == DateTime.MinValue ? "" : Model.applyStartTime.ToString("yyyy-MM-dd"))" />
                        <label class="add-on"><i class="iconfont">&#xe8d6;</i></label> &#8211;
                        <input name="applyEndTime" id="applyEndTime" type="text" class="text w70" value="@(Model.applyEndTime == DateTime.MinValue ? "" : Model.applyEndTime.ToString("yyyy-MM-dd"))" />
                        <label class="add-on"><i class="iconfont">&#xe8d6;</i></label>
                    </td>
                    <th class="w60">@T("处理状态")</th>
                    <td class="w80">
                        @Html.DropDownList("state", new List<SelectListItem>
                        {
                            new SelectListItem { Text = T("全部").ToString(), Value = "0", Selected = Model.state == 0 },
                            new SelectListItem { Text = T("待审核").ToString(), Value = "1", Selected = Model.state == 1 },
                            new SelectListItem { Text = T("同意").ToString(), Value = "2", Selected = Model.state == 2 },
                            new SelectListItem { Text = T("不同意").ToString(), Value = "3", Selected = Model.state == 3 },
                            new SelectListItem { Text = T("已完成").ToString(), Value = "4", Selected = Model.state == 4 }
                        }, new { @class = "text" })
                    </td>
                    <td class="w160"><input type="text" class="text" name="key" placeholder="@T("姓名/订单号/退款编号")" value="@Model.key" /></td>
                    <td class="w70 tc">
                        <input type="submit" class="submit" value="@T("搜索")" />
                    </td>
                </tr>
            </table>
        </form>
        <table class="dssc-default-table">
            <thead>
                <tr>
                    <th class="w10"></th>
                    <th colspan="1">@T("商品/订单号/退款号")</th>
                    <th class="w150">@T("退款金额")</th>
                    <th class="w150">@T("买家会员名")</th>
                    <th class="w150">@T("处理状态")</th>
                    <th class="w150">@T("退货状态")</th>
                    <th class="w150">@T("操作")</th>
                </tr>
            </thead>
            <tbody>
                @{
                    foreach (RefundReturn item in Model.list)
                    {
                        <tr>
                            <th colspan="20">

                                <span class="ml10" title="@item.StoreName">
                                    @T("订单编号")：<em><a href="javascript:void(0);" >@item.OrderSn</a></em>
                                </span>
                                <span class="ml10">
                                    @T("退款编号")：<em>@item.RefundSn</em>
                                </span>
                                <span>@T("申请时间")：<em class="goods-time">@(UnixTime.ToDateTime(item.RefundreturnAddTime).ToString("yyyy-MM-dd HH:mm:ss"))</em></span>
                            </th>
                        </tr>
                        var orderGoods = OrderGoods.FindAllByOrderId(item.OrderId);
                        int rowspanRow = orderGoods.Count() >= 2?2:orderGoods.Count();
                        int rows = orderGoods.Count();
                        for (int i = 0; i < rows; i++)
                        {
                            OrderGoods goods = orderGoods[i];
                            <tr class="bd-line @(i > 1 ? "row-display" : "")" name="tr@(item.Id)">
                                <td class="w60">
                                    <div class="pic-thumb">
                                        <a href="@Url.Action("Index", "Goods", new{Area = "" ,goodsId = goods.GoodsId})" target="_blank">
                                            <img src="@goods.ImagePath" />
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <dl style="text-align:left">
                                        <dt>
                                            <a href="@Url.Action("Index", "Goods", new{Area = "" ,goodsId = goods.GoodsId})" target="_blank">@goods.GoodsName</a>
                                            <p>@goods.SkuDetail?.SpecValueDetail(lid)</p>
                                        </dt>
                                        <dt>@<EMAIL>*@goods.GoodsNum</dt>
                                    </dl>
                                </td>
                                @if (goods == orderGoods.First())
                                {
                                    <td rowspan="@rowspanRow">@<EMAIL></td>
                                    <td rowspan="@rowspanRow">@item.UName</td>
                                    <td rowspan="@rowspanRow">
                                        @switch (item.RefundreturnSellerState)
                                        {
                                            case 1:
                                                <p style="color: gray;">@T("待审核")</p>
                                                break;
                                            case 2:
                                                <p style="color: green;">@T("同意")</p>
                                                break;
                                            case 3:
                                                <p style="color: red;">@T("不同意")</p>
                                                break;
                                            case 4:
                                                <p style="color: blue;">@T("已退款")</p>
                                                break;
                                        }
                                    </td>
                                    <td rowspan="@rowspanRow">
                                        @if (item.ReturnType==1)
                                        {
                                            <p style="color: gray;">@T("弃货")</p>
                                        }
                                        else
                                        {
                                            switch (item.RefundreturnGoodsState)
                                            {
                                                case 1:
                                                    if (item.ReturnState == 2)
                                                    {
                                                        <p style="color: orange;">@T("待货物追回")</p>
                                                    }
                                                    else
                                                    {
                                                        <p style="color: orange;">@T("待买家退货")</p>
                                                    }
                                                    break;
                                                case 2:
                                                    <p style="color: green;">@T("待仓库收货")</p>
                                                    break;

                                                case 3:
                                                    <p style="color: red;">@T("仓库未收到")</p>
                                                    break;
                                                case 4:
                                                    <p style="color: blue;">@T("货物已退回")</p>
                                                    break;
                                            }
                                        }


                                    </td>
                                    <td rowspan="@rowspanRow" class="dscs-table-handle">
                                        @if (item.RefundreturnSellerState==1)
                                        {
                                            <span>
                                                <a href="@Url.Action("Refund",new{id = item.Id,type=Model.type})" class="btn-blue">
                                                    <i class="iconfont">&#xe731;</i>
                                                    <p>@T("处理")</p>
                                                </a>
                                            </span>
                                        }
                                        else
                                        {
                                            <span> <a href="@Url.Action("RefundDetail",new {id = item.Id})" class="btn-orange"><i class="iconfont">&#xe70b;</i><p>@T("查看")</p></a></span>
                                            @if (item.RefundreturnGoodsState == 4 && item.RefundreturnSellerState!=4)//如果仓库已收到货，显示收货按钮
                                            {
                                                <span>
                                                    <a href="javascript:void(0)" class="btn-green" onclick="processRefund(@item.Id)">
                                                        <i class="iconfont">&#xe6e9;</i>
                                                        <p>@T("退款")</p>
                                                    </a>
                                                </span>
                                            }
                                        }

                                    </td>
                                }
                            </tr>
                        }
                        @if (orderGoods.Count() > 2)
                        {
                            <tr class="bd-line">
                                <th colspan="20">
                                    <span class="ml10">@T("共")<em>@orderGoods.Count()</em>@T("件商品")</span>
                                    <span>
                                        <a href="javascript:void(0)" class="btn-green" onclick="rowShow(@item.Id,@rows,true)">
                                            <i class="iconfont">&#xe73a;</i>
                                            @T("展开")
                                        </a>
                                    </span>
                                    <span style="display:none">
                                        <a href="javascript:void(0)" class="btn-green" onclick="rowShow(@item.Id,@rows,false)">
                                            <i class="iconfont">&#xe738;</i>
                                            @T("收起")
                                        </a>
                                    </span>
                                </th>
                            </tr>
                        }
                    }
                }
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="20">
                        <div class="pagination">
                            <ul class="pagination">
                                @Html.Raw(Model.PageHtml)
                            </ul>
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
        <style>
           .dssc-default-table td, .dssc-default-table th {
                border: 1px solid #ddd;
                padding: 8px;
            }
            .dssc-default-table {
                border-collapse: collapse;
            }
            .sep-row {
                border: none !important;
                height: 10px;
                background: #f5f5f5;
            }
            .dssc-default-table tfoot td {
                border: none;
                padding: 15px 0;
            }
        </style>
        <script>
            $(function () {
                $('#applyStartTime').datepicker({dateFormat: 'yy-mm-dd'});
                $('#applyEndTime').datepicker({dateFormat: 'yy-mm-dd'});
            });

            function rowShow(id, rows, isShow) {
                var trs = $('tr[name="tr' + id + '"]');
                var firstTr = trs.first();
                
                // 显示或隐藏行
                trs.each(function(index) {
                    if (index > 1) {
                        $(this).toggle(isShow);
                    }
                });
                
                // 更新rowspan值
                var newRowspan = isShow ? rows : 2;
                firstTr.find('td[rowspan]').each(function() {
                    $(this).attr('rowspan', newRowspan);
                });
                
                // 切换展开/收起按钮显示
                $('a[onclick="rowShow(' + id + ',' + rows + ',true)"]').parent().toggle(!isShow);
                $('a[onclick="rowShow(' + id + ',' + rows + ',false)"]').parent().toggle(isShow);
            }

            function processRefund(id) {
                if (!id) {
                    layer.msg('@T("没有选择退款记录")', { icon: 2 });
                    return;
                }

                layer.confirm('@T("确认是否退款")', {
                    btn: ['@T("确认")', '@T("取消")'],
                    title: '@T("")'
                }, function() {
                    var loading = layer.load(1, { shade: [0.1, '#fff'] });

                    $.ajax({
                        url: '@Url.Action("RefundProcessing")',
                        type: 'POST',
                        data: { id: id },
                        success: function(res) {
                            layer.close(loading);
                            if (res.success) {
                                layer.msg(res.msg, { icon: 1 });
                                setTimeout(function() {
                                    location.reload();
                                }, 1000);
                            } else {
                                layer.msg(res.msg, { icon: 2 });
                            }
                        },
                        error: function() {
                            layer.close(loading);
                            layer.msg('@T("网络错误，请稍后重试")', { icon: 2 });
                        }
                    });
                });
            }
        </script>
    </div>
</div>