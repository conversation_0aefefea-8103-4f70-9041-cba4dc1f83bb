﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace B2B2CShop.Entity;

/// <summary>商品阶梯价格表</summary>
public partial interface IGoodsTieredPrice
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>商品ID</summary>
    Int64 GoodsId { get; set; }

    /// <summary>商品公共表ID</summary>
    Int64 GoodsCommonId { get; set; }

    /// <summary>店铺ID</summary>
    Int64 StoreId { get; set; }

    /// <summary>商品SkuID</summary>
    Int64 SkuId { get; set; }

    /// <summary>最小购买数量</summary>
    Int32 MinQuantity { get; set; }

    /// <summary>最大购买数量</summary>
    Int32 MaxQuantity { get; set; }

    /// <summary>原价</summary>
    Decimal OriginalPrice { get; set; }

    /// <summary>销售价</summary>
    Decimal Price { get; set; }

    /// <summary>是否启用</summary>
    Boolean Enabled { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
