﻿@{

}
<div class="seller_left">
    <div class="seller_left_1">
        <div class="logo">
            <a href="/index.php/home/<USER>/index.html">
                <img src="\uploads\common\default_store_avatar.png" />
            </a>
        </div>
        <div class="sidebar">
            @* <a href="/index.php/home/<USER>/index.html?store_id=1" target="_blank">
                <i class="iconfont">&#xe6da;</i>@T("店铺")
            </a> *@
            <a href="@Url.Action("Index", "SellerGoodsOnline")" @(ViewBag.LeftMenu == "Goods" ? Html.Raw("class=\"active\"") : "")><i class="iconfont">&#xe732;</i>@T("商品")</a>
            <a href="@Url.Action("Index", "SellerOrder")" @(ViewBag.LeftMenu == "Orders" ? Html.Raw("class=\"active\"") : "")><i class="iconfont">&#xe71f;</i>@T("订单")</a>
            @* <a href="/index.php/home/<USER>/index.html"><i class="iconfont">&#xe704;</i>@T("促销")</a> *@
            <a href="@Url.Action("Index", "Home")" @(ViewBag.LeftMenu == "Store" ? Html.Raw("class=\"active\"") : "")><i class="iconfont">&#xe663;</i>@T("店铺")</a>
            @* <a href="@Url.Action("Index", "SellerRefund")" @(ViewBag.LeftMenu == "AfterSale" ? Html.Raw("class=\"active\"") : "")><i class="iconfont">&#xe6ab;</i>@T("售后")</a> *@
            @*<a href="/index.php/home/<USER>/index.html"><i class="iconfont">&#xe6a3;</i>@T("统计")</a>
            <a href="/index.php/home/<USER>/index.html"><i class="iconfont">&#xe61c;</i>@T("客服")</a>
            <a href="/index.php/home/<USER>/account_list.html"><i class="iconfont">&#xe702;</i>@T("账号")</a>
            <a href="/index.php/home/<USER>/goods_list.html"><i class="iconfont">&#xe6ed;</i>@T("分销")</a> *@
        </div>
        <div class="mb">
            <a href="@Url.Action("Index", "Logout", new { Area = "" })">@T("退出")</a>
        </div>
    </div>
    <div class="seller_left_2">
        @if (ViewBag.LeftMenu == "Store")
        {
            <div class="mt">
                @T("店铺")
            </div>
            <div class="mc">
                @* <a href="/index.php/home/<USER>/index.html">@T("店铺资金")</a>
                <a href="/index.php/home/<USER>/index.html">@T("店铺保证金")</a>
                <a href="/index.php/home/<USER>/index.html">@T("店铺信息")</a> *@
                <a href="@Url.Action("Index", "Home")" @(ViewBag.LeftChileMenu == "Store" ? Html.Raw("class=\"active\"") : "")>@T("店铺概况")</a>
                <a href="@Url.Action("Index", "SellerSetting")" @(ViewBag.LeftChileMenu == "Setting" ? Html.Raw("class=\"active\"") : "")>@T("店铺设置")</a>
                @* <a href="/index.php/home/<USER>/page_list.html">@T("PC")</a>
                <a href="/index.php/home/<USER>/h5_page_list.html">@T("H5")</a>
                <a href="/index.php/home/<USER>/index.html">@T("店铺导航")</a>
                <a href="/index.php/home/<USER>/index.html">@T("店铺动态")</a>
                <a href="/index.php/home/<USER>/index.html">@T("店铺分类")</a>
                <a href="/index.php/home/<USER>/index.html">@T("自提门店")</a>
                <a href="/index.php/home/<USER>/index.html">@T("品牌申请")</a> *@
            </div>
        }
        else if (ViewBag.LeftMenu == "Goods")
        {
            <div class="mt">
                @T("商品")
            </div>
            <div class="mc">
                <a href="@Url.Action("Index", "SellerGoodsAdd")" @(ViewBag.LeftChileMenu == "GoodsAdd" ? Html.Raw("class=\"active\"") : "")>@T("商品发布")</a>
                @* <a href="/index.php/home/<USER>/index.html">@T("商品CSV导入")</a> *@
                <a href="@Url.Action("Index", "SellerGoodsOnline")" @(ViewBag.LeftChileMenu == "GoodsOnline" ? Html.Raw("class=\"active\"") : "")>@T("出售中的商品")</a>
                <a href="@Url.Action("Index", "SellerGoodsInWarehourse")" @(ViewBag.LeftChileMenu == "GoodsInWarehourse" ? Html.Raw("class=\"active\"") : "")>@T("仓库中的商品")</a>
                <a href="@Url.Action("Index", "SellerGoodsSpecManage")" @(ViewBag.LeftChileMenu == "GoodsSpecManage" ? Html.Raw("class=\"active\"") : "")>@T("商品规格")</a>
                <a href="@Url.Action("Index", "SellerPictureManage")" @(ViewBag.LeftChileMenu == "PictureManage" ? Html.Raw("class=\"active\"") : "")>@T("图片空间")</a>
                @*<a href="/index.php/home/<USER>/index.html">@T("关联版式")</a>
                <a href="/index.php/home/<USER>/index.html">@T("视频库")</a>
                <a href="/index.php/home/<USER>/index.html">@T("店铺服务")</a> *@
                <a href="@Url.Action("Index", "SellerGoodsMaterials")" @(ViewBag.LeftChileMenu == "GoodsMaterials" ? Html.Raw("class=\"active\"") : "")>@T("商品物料")</a>
            </div>
        }
        else if (ViewBag.LeftMenu == "Orders")
        {
            <div class="mt">
                @T("订单")
            </div>
            <div class="mc">
                <a href="@Url.Action("Index", "SellerOrder")" @(ViewBag.LeftChileMenu == "Orders" ? Html.Raw("class=\"active\"") : "")>@T("交易订单")</a>
                <a href="@Url.Action("Index", "SellerRefund")" @(ViewBag.LeftChileMenu == "Refund" ? Html.Raw("class=\"active\"") : "")>@T("退款退货")</a>
                @*<a href="/index.php/home/<USER>/index.html">@T("虚拟兑码订单")</a>
                <a href="@Url.Action("Index", "SellerOrderDelivery")" @(ViewBag.LeftChileMenu == "OrdersDelivery" ? Html.Raw("class=\"active\"") : "")>@T("发货管理")</a>
                <a href="@Url.Action("Index", "SellerDeliverySetting")" @(ViewBag.LeftChileMenu == "DeliverySetting" ? Html.Raw("class=\"active\"") : "")>@T("发货设置")</a>
                <a href="/index.php/home/<USER>/index.html">@T("评价管理")</a>
                <a href="/index.php/home/<USER>/index.html">@T("售卖区域")</a> *@
            </div>
        }
        @* else if (ViewBag.LeftMenu == "AfterSale")
        {
            <div class="mt">
                @T("售后")
            </div>
            <div class="mc">
                <a href="/index.php/home/<USER>/index.html">@T("虚拟兑码订单")</a>
                <a href="@Url.Action("Index", "SellerOrderDelivery")" @(ViewBag.LeftChileMenu == "OrdersDelivery" ? Html.Raw("class=\"active\"") : "")>@T("发货管理")</a>
                <a href="@Url.Action("Index", "SellerDeliverySetting")" @(ViewBag.LeftChileMenu == "DeliverySetting" ? Html.Raw("class=\"active\"") : "")>@T("发货设置")</a>
                <a href="/index.php/home/<USER>/index.html">@T("评价管理")</a>
                <a href="/index.php/home/<USER>/index.html">@T("售卖区域")</a> 
            </div>
        }*@
    </div>
</div>
@* 侧边栏结束 *@