 /* 首页- 主体 --------------- 开始 ------------ -----------------------------------------------------*/
 @media screen and (max-width: 1200px) {
     .optionsBox2 {
         margin-left: 10% !important;
     }

     .commentHeader {
         margin-left: 10% !important;
     }

     .commentBox {
         margin-left: 10% !important;
     }

     .goodsDataTitle1 {
         width: 62.5% !important;
         margin-left: 12.5% !important;
     }

     .goodsDataTable {
         width: 62.5% !important;
         margin-left: 12.5% !important;
     }

     .profilePDF {
         width: 62.5% !important;
         margin-left: 12.5% !important;
     }

     .shoppingGuide {
         width: 62.5% !important;
         margin-left: 12.5% !important;
     }
 }

 /* 商品详情信息页面 */
 ._main {
     margin-left: 15%;
     width: 62.5%;
 }

 .productBox {
     padding: 1vw 0px;
     /* height: 40vh; */
     display: flex;
     justify-content: center;
     border-bottom: 1px solid var(--text-color4);
 }

 .left {
     width: 43%;
     display: flex;
     max-height: 40vh;
 }

 .left-image {
     width: 15%;
     display: flex;
     flex-direction: column;
     gap: .5vw;
     height: 100%;
     align-self: flex-start;
     max-height: 40vh;
 }

 .thumbnail-item {
     width: 52px;
     height: 52px;
     border-radius: 8px;
     border: 1px solid transparent;
 }

 .imgBorder {
     border: 1px solid var(--blue-deep);
 }


 .left>div>div>img {
     width: 100%;
     height: 100%;
     object-fit: contain;
     border-radius: 8px;
 }

 .left>div:nth-child(2) {
     width: auto;
     height: 100%;
 }

 .left>div:nth-child(2)>div {
     margin: auto;
     width: 90%;
     height: 100%;
     border: 1px solid var(--line);
     display: flex;
     justify-content: center;
     /* background-color: var(--line); */
 }

 .left>div:nth-child(1)>div>img {
     width: 100%;
     height: 100%;
     object-fit: contain;
     /* border: 2px solid red; */
     min-width: 40px;
 }

 .left>div:nth-child(2)>div>img {
     width: 100%;
     height: 100%;
     object-fit: contain;
 }

 .right {
     margin-left: auto;
     width: 55%;
 }

 .productTitle {
     padding-bottom: .5vw;
     font-size: 16px;
 }

 /* 选中第2 - 7 */
 .right>div:nth-child(n + 2):nth-child(-n + 7) {
     justify-content: left;
 }

 .starBox {
     border-bottom: 1px solid var(--line);
 }

 .configBox {
     margin-top: .3vw;
     display: flex;
     flex-wrap: wrap;
     border-bottom: 1px solid var(--line);
     padding: .5vw 0px;
 }

 .configBox>div:nth-child(n+2) {
     margin: .5vw;
     padding: .5vw 1vw;
     width: fit-content;
     border: 1px solid var(--line);
     border-radius: 5px;
     cursor: pointer;
     height: 20px;
     line-height: 20px;
 }

 /* .configBox>div:nth-child(5n-2){
    margin-left: 0px;
    padding-left: 0px;
    text-indent: .5vw;
} */
 .configBox>div[data-select="true"] {
     color: var(--blue-deep);
     border: 1px solid var(--blue-deep) !important;
     user-select: none;
 }

 .booksBtnBox {
     padding: .5vw 0px;
     margin: .5vw;
     /* border: 1px solid ; */
 }

 .booksBtnBox>button {
     padding: .5vw 1vw;
     margin-left: 0vw;
     background-color: white;
     border: 1px solid var(--blue-deep);
     color: var(--blue-deep);
     border-radius: 5px;
 }

 /* 商品参数 / 用户评价 */
 .commentBox {
     /* padding: 0vw 1vw 0vw 1vw; */
     width: 62.5%;
     margin-left: 15%;
     text-align: left;
     overflow: auto;
     /* border: 2px solid ; */
 }

 .optionsBox2 {
     position: sticky;
     top: 0%;
     width: 62.5%;
     margin-left: 15%;
     padding: 0vw 0vw;
     background-color: white;
     line-height: 1.5vw;
     /* border: 2px solid ; */
     z-index: 9999;
 }

 .titleBox1 {
     margin-top: 1.5vw;
     padding-bottom: 1vw;
     justify-content: left;
     font-size: 20px;
     cursor: pointer;
     border-bottom: 1px solid var(--line);
     /* text-indent: 1vw; */
 }

 .commentHeader {
     position: sticky;
     top: 0%;
     width: 62.5%;
     margin-left: 15%;
     padding-bottom: 1vw;
     background-color: white;
     /* border: 2px solid ; */
     z-index: 9999;
 }

 .commentHeader>.titleBox {
     text-indent: 2vw;
     margin-top: 1vw;
     /* border: 2px solid ; */
     justify-content: left;
     padding-bottom: .7vw;
     font-size: 15px;
     cursor: default;
 }

 .commentBox>div:not(:first-child) {
     color: var(--text-color);
     /* border: 1px solid ; */
 }

 .commentContainer {
     margin-left: 4%;
     /* border: 1px solid ; */
 }

 .optionsBox {
     justify-content: left;
     margin-left: 2.5vw;
     background-color: white;
 }

 .optionsBox>div {
     margin-left: 3%;
     font-size: 15px;
     color: var(--text-color2);
     cursor: pointer;
 }

 .optionsBox>.titleSelect {
     color: var(--text-color) !important;
 }

 .optionsBox>.titleSelect::before {
     width: 100%;
     background-color: var(--text-color) !important;
 }

 .comment {
     border-bottom: 1px solid var(--line);
     color: var(--text-color3);
     padding: 15px 0px;
 }

 .commentUserInfo {
     justify-content: left;
     height: 48px;
 }

 .userInfo {
     width: 80%;
     height: 100%;
     /* border: 1px solid ; */
     margin-left: 1vw;
 }

 .ago {
     color: var(--info);
     margin-top: 5px;
 }

 .avatar>img {
     padding: 3px;
     width: 100%;
     border-radius: 50%;
     border: 1px solid var(--line);
 }

 .commentText {
     margin-top: 18px;
     margin-left: 7%;
     /* border: 1px solid var(--line); */
 }

 .imageBox {
     margin-top: .5vw;
     display: flex;
     flex-wrap: wrap;
 }

 .imageBox>img {
     width: 20%;
     border-radius: 10px;
 }

 .imageBox>img:not(:first-child) {
     margin-left: 2%;
 }

 .allComments {
     padding: 2vw;
     text-align: center;
     font-size: 15px;
     color: var(--text-color2);
     cursor: pointer;
 }

 .allComments>span {
     padding: 5px 2vw;
     border: 1px solid var(--line);
     border-radius: 5px;
 }

 .allComments>span:hover {
     color: var(--blue-deep);
 }

 /* <!-- & 商品参数 --> */
 .goodsDataTitle1 {
     /* position: sticky;
    top: 0%; */
     margin-left: 15%;
     width: 61.5%;
     font-size: 1vw;
     padding: 1vw;
     z-index: 3;
 }

 .goodsDataTable {
     margin-left: 15%;
     width: 62.5%;
     /* padding-bottom: 1vw; */
     /* border: 1px solid var(--text-color4); */
     border-radius: 12px;
     font-size: .8vw;
     color: var(--text-color1);
     overflow: hidden;
 }

 .goodsDataTable>table>thead>tr>th {
     border: 1px solid #BABABA;
     box-sizing: border-box;
 }

 .goodsDataTable>table>tbody>tr>td {
     padding: 1vw;
     border: 1px solid #BABABA;
     box-sizing: border-box;
 }

 .likes {
     padding: 1vw 0vw 1vw 1vw;
     border-bottom: 1px solid var(--text-color4);
 }

 /* PDF  */
 .profilePDF {
     width: 62.5%;
     height: 68vh;
     margin-left: 15%;
     position: relative;
 }

 .profilePDF iframe {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     border: none;
 }



 .shoppingGuide {
     width: 62.5%;
     margin: 1vw 1vw 1vw 15%;
     border: 1px solid var(--text-color4);
 }

 .shoppingGuide>img {
     width: 100%;
 }

 /* <!-- 右侧边栏 --> */
 .orderAside {
     position: absolute;
     right: 3.5%;
     top: 200px;
     width: 15%;
     min-width: 210px;
     border-radius: 10px;
     background-color: white;
     padding: 1vw;
     box-shadow: 0 0 4px #ccc;
 }

 .orderAside>div {
     text-indent: .4vw;
 }

 .orderAside>div {
     margin: .5vw 0px;
 }

 .asideTitle {
     color: #FE3846;
     font-size: .7vw;
     padding: .2vw .5vw;
     background-color: #fe38452d;
     border-radius: 10px;
     display: flex;
     place-items: center;
 }

 .discount {
     margin-left: 5px;
     color: #FE3846;
 }

 .price {
     padding-top: .5vw;
     font-size: 1vw;
 }

 .tableBox {
     border-radius: 15px !important;
     overflow: hidden;
     padding: 0vw;
     background-color: #F5F5F5;
     color: var(--text-color3);
     font-size: .8vw;
 }

 .gray {
     color: #979997;
 }

 .aside_btnBox2>div {
     background-color: #F5F5F5;
     margin-top: 10px;
     padding: .4vw 1.1vw;
     border-radius: 45px;
     white-space: nowrap;
 }

 .asideTextTitle {
     font-size: .9vw;
 }

 .totalPrice {
     color: var(--danger);
     font-size: 1.1vw;
     font-weight: bold;
     padding: 1vw 0;
 }

 .totalValue {
     font-size: 2.4vw !important;
 }

 .model,
 .allStock {
     margin: 1vw 0;
 }

 .count {
     margin: .5vw 0;
 }

 .storeTable th,
 .storeTable td {
     height: 4vw;
 }

 .asideBtn {
     width: 96%;
     border-radius: 45px;
     margin: auto;
     padding: .5vw 0px;
 }

 /* 服务申明 */
 .img {
     position: absolute;
     top: 22%;
     right: 2%;
     width: 20%;
 }

 .serverInfo {
     position: absolute;
     top: 920px;
     right: 3%;
     width: 17%;
     color: var(--text-color1);
 }

 .serverTitle {
     padding: .4vw 0px;
     font-size: 1vw;
     color: var(--text-color1);
     justify-content: left;
 }

 .serverItem {
     padding: .2vw 0px;
     margin-left: 1.9vw;
     justify-content: left;
 }

 .serverItem>.icon-ai210 {
     color: var(--blue-deep);
 }

 .paymentMethods>img {
     margin-right: .2vw;
 }