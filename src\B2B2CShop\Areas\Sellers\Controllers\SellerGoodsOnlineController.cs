﻿using B2B2CShop.Dto;
using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;
using Newtonsoft.Json;
using Pek;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCube;
using Pek.Webs;
using System.ComponentModel;
using System.Dynamic;
using System.Security.Claims;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Sellers.Controllers;

/// <summary>
/// 商家中心商品控制器
/// </summary>
[SellersArea]
[SellersAuthorize]
public class SellerGoodsOnlineController : PekBaseControllerX {

    /// <summary>
    /// 商品首页
    /// </summary>
    /// <returns></returns>
    public IActionResult Index(string keyword, int search_type = 0, int page = 1, int limit = 10)
    {
        dynamic viewModel = new ExpandoObject();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };

        keyword = keyword.SafeString().Trim();

        var userId = ManageProvider.User?.ID ?? 0;
        if (search_type == 0)
        {
            viewModel.list = Goods.FindByGoodsNameAndUserId(keyword, userId, 1,pages);
        }
        else if (search_type == 1)
        {
            viewModel.list = Goods.FindByGoodsSerialAndUserId(keyword, userId,1, pages);
        }
        else
        {
            viewModel.list = Goods.FindByIdAndUserId(keyword, userId,1, pages);
        }
        viewModel.page = page;
        viewModel.keyword = keyword;
        viewModel.search_type = search_type;
        viewModel.PageHtml = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Index"), new Dictionary<string, string> { { "keyword", keyword }, { "search_type", search_type.ToString() } });
        return View(viewModel);
    }

    /// <summary>
    /// 删除商品
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [DisplayName("删除商品")]
    public IActionResult Delete(string ids)
    {
        var userId = ManageProvider.User?.ID ?? 0;
        //检查是否有权限
        var goodsCommons = GoodsCommon.CheckOwner(ids, userId);
        if (!goodsCommons)
        {
            return Json(new DResult { success = false, code = 10001, msg = GetResource("无权限") });
        }
        var res = new DResult();
        GoodsCommon.DelByIds(ids);

        Goods.DelByCommonIds(ids);

        GoodsLan.DeleteByGIds(ids);//删除商品翻译表

        GoodsSKUDetail.DeleteByGIds(ids);//删除商品SKU详情

        GoodsImages.DeleteByGIds(ids);//删除商品图片

        GoodsImagesLan.DeleteByGIds(ids);//删除商品翻译表图片

        GoodsExAttributes.DeleteByGIds(ids);//删除商品扩展属性

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>
    /// 下架商品
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [DisplayName("下架商品")]
    public IActionResult Unshow(string ids)
    {
        var userId = ManageProvider.User?.ID ?? 0;
        //检查是否有权限
        var goodsCommons = GoodsCommon.CheckOwner(ids, userId);
        if (!goodsCommons)
        {
            return Json(new DResult { success = false, code = 10001, msg = GetResource("无权限") });
        }
        var res = new DResult();
        GoodsCommon.UnshowByIds(ids);
        GoodsCommon.Meta.Cache.Clear("", true);

        Goods.UnshowByCommonIds(ids);
        GoodTypeSpec.Meta.Cache.Clear("", true);

        res.success = true;
        res.code = 10000;
        res.msg = GetResource("下架成功");
        return Json(res);
    }

    /// <summary>
    /// 编辑商品
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑商品")]
    public IActionResult EditGoodsDetail(long id)
    {
        var userId = ManageProvider.User?.ID ?? 0;
        var model = Goods.FindById(id);
        if (model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品不存在") });
        }

        // 检查是否有权限
        if (model.CreateUserID != userId)
        {
            return Prompt(new PromptModel { Message = GetResource("无权限") });
        }
        return View(model);
    }

    /// <summary>
    /// 编辑商品
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("编辑商品信息")]
    public IActionResult EditGoodsDetail(Int64 goodsId, String goodsName, String advWord, Int64 merchantMaterial, String goodsSerial, String goodsImage, String videoUrl, String goodsBody, String goods_Mobilebody, Int32? brandId, Int64 plateidTop, Int64 plateidBottom, Decimal goodsFreight, Int32 goodsVat, String goodsStcids, Int32 isGoodsFCode, Int32 isAppoint, Int64 appointSatedate, Int32? isVirtual, Int32? virtualType, Int64? VirtualIndate, Int32? virtualLimit,Int64 goodsShelftime, Int32 goodsCommend, List<TieredPriceInput> tieredPrice,String seotitle,String seokeys,String seodescription,String deliveryTime,String deliveryDate)
    {
        if (goodsName.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("商品名称为空") });
        if (merchantMaterial.IsNull()) return Prompt(new PromptModel { Message = GetResource("商品物料为空") });
        var material = MerchantMaterial.FindById(merchantMaterial);
        if (material == null) return Prompt(new PromptModel { Message = GetResource("商品物料不存在") });
        var goods = Goods.FindById(goodsId);//添加商品表
        if (goods==null) return Prompt(new PromptModel { Message = GetResource("商品信息不存在") });
        var goodsCommon = GoodsCommon.FindById(goods.GoodsCommonId);//添加商品公共表
        if (goodsCommon==null) return Prompt(new PromptModel { Message = GetResource("商品信息不存在") });
        var storeEntity = Store.FindByUId(ManageProvider.User?.ID ?? 0);
        if (storeEntity == null) return Prompt(new PromptModel { Message = GetResource("店铺信息不存在") });

        try
        {
            using (var tran1 = GoodsCommon.Meta.CreateTrans())
            {
                #region 商品公共表
                goodsCommon.Name = goodsName;
                goodsCommon.AdvWord = advWord;

                goodsCommon.StoreId = storeEntity.Id;
                goodsCommon.StoreName = storeEntity.Name;
                //goodsCommon.BrandId = brandId.Value;
                //goodsCommon.BrandName = Brand.FindById(brandId.Value)?.Name ?? "";
                goodsCommon.GoodsImage = goodsImage;
                goodsCommon.GoodsVideoName = videoUrl;
                goodsCommon.GoodsBody = goodsBody;
                goodsCommon.MobileBody = goods_Mobilebody;
                goodsCommon.GoodsShelftime = goodsShelftime;
                goodsCommon.GoodsSerial = goodsSerial;
                goodsCommon.GoodsCommend = goodsCommend;
                goodsCommon.GoodsFreight = goodsFreight;
                goodsCommon.GoodsVat = goodsVat;
                goodsCommon.GoodsStcids = goodsStcids;
                goodsCommon.PlateidTop = plateidTop;
                goodsCommon.PlateidBottom = plateidBottom;
                //goodsCommon.IsVirtual = isVirtual.Value;
                //goodsCommon.VirtualType = virtualType.Value;
                //goodsCommon.VirtualIndate = VirtualIndate.Value;
                //goodsCommon.VirtualLimit = virtualLimit.Value;
                goodsCommon.IsGoodsFCode = isGoodsFCode;
                goodsCommon.IsAppoint = isAppoint;
                goodsCommon.AppointSatedate = appointSatedate;
                goodsCommon.GoodsPrice = material.Price;
                goodsCommon.GoodsMarketPrice = material.MarketPrice;
                goodsCommon.GoodsCostPrice = material.CostPrice;
                goodsCommon.GoodsDiscount = material.Discount;
                goodsCommon.Update();
                #endregion

                #region 商品表
                goods.Name = goodsName;
                goods.AdvWord = advWord;
                goods.StoreId = storeEntity.Id;
                goods.StoreName = storeEntity.Name;
                //goods.BrandId = brandId.Value;
                goods.GoodsSerial = goodsSerial;
                if (goods.GoodsImage!= goodsImage&& !goodsImage.IsNullOrEmpty())//商品主图发生变化
                {
                    GoodsImages.SetMainPicture(goods.GoodsImage ?? "", goodsImage,goodsCommon.Id,goods.StoreId);//更换默认主图
                }
                goods.GoodsImage = goodsImage;
                goods.GoodsVideoName = videoUrl;
                goods.GoodsFreight = goodsFreight;
                goods.GoodsVat = goodsVat;
                goods.GoodsCommend = goodsCommend;
                goods.GoodsStcids = goodsStcids;
                //goods.IsVirtual = isVirtual.Value;
                //goods.VirtualIndate = VirtualIndate.Value;
                //goods.VirtualLimit = virtualLimit.Value;
                goods.IsGoodsFCode = isGoodsFCode;
                goods.IsAppoint = isAppoint;
                var skuList = GoodsSKUDetail.FindAllByGoodsId(goods.Id);
                if (skuList.Count==0)//如果不存在SKU属性
                {
                    goods.MerchantMaterial = merchantMaterial;
                    goods.MaterialIds = merchantMaterial.SafeString();
                }
                goods.SeoTitle = seotitle;
                goods.SeoKeys = seokeys;
                goods.SeoDescription = seodescription;
                goods.GoodsPrice = material.Price;
                goods.GoodsMarketPrice = material.MarketPrice;
                goods.DeliveryTime = deliveryTime;
                goods.DeliveryDate = deliveryDate;
                #endregion

                #region 商品分类属性
                ///商品扩展属性表
                List<ClassAttributes> attributeList = ClassAttributes.FindAllByClassIdLan(goods.CId, WorkingLanguage.Id);
                var goodsExAttributes = GoodsExAttributes.FindByGId(goods.Id);
                if (goodsExAttributes==null)
                {
                    goodsExAttributes = new GoodsExAttributes();
                    goodsExAttributes.GoodsId = goods.Id;
                    goodsExAttributes.ClassId = goods.CId;
                }
                foreach (var item in attributeList)
                {
                    string field = GetRequest($"classAttribute_{item.MappingField}").SafeString().Trim();
                    if (field.IsNullOrEmpty()) continue;
                    var property = goodsExAttributes.GetType().GetProperty(item.MappingField ?? "");
                    if (property != null)
                    {
                        var attList = GoodsClassExAttributes.FindAllByClassIdAndFlied(goods.CId, item.MappingField);//插入分类属性扩展表
                        var oldField = goodsExAttributes?.GetProperty(item.MappingField);
                        if (goodsExAttributes != null && field != oldField)//如果是修改
                        {
                            var attsother = GoodsExAttributes.FindAllByClassIdAndField(goods.CId, item.MappingField, oldField).Where(e=>e.GoodsId!=goods.Id);
                            if (!attsother.Any()) //判断其他商品是否有这个属性，如果没有的就删除这条记录
                            {
                                var gcexatt = GoodsClassExAttributes.FindByClassAndFieldValue(goods.CId, item.MappingField, oldField);
                                if (gcexatt!=null)
                                {
                                    gcexatt.Delete();
                                }
                            }
                        }
                        if (!attList.Contains(field))
                        {
                            var attModel = new GoodsClassExAttributes();
                            attModel.SetProperty(item.MappingField, field);
                            attModel.ClassId = goods.CId;
                            attModel.Insert();
                        }
                      
                        goodsExAttributes.SetProperty(item.MappingField, field);
                    }
                }
                goodsExAttributes.Save();
                ///分类属性扩展表
                #endregion

                #region 商品/图片翻译表
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var localizationSettings = LocalizationSettings.Current;
                var glList = new List<GoodsLan>();
                if (localizationSettings.IsEnable)
                {
                    foreach (var item in Languagelist)
                    {
                        var lanModel = GoodsLan.FindByGIdAndLId(goodsCommon.Id, item.Id);
                        if (lanModel==null)
                        {
                            lanModel = new GoodsLan();
                            lanModel.GId = goodsCommon.Id;
                            lanModel.LId = item.Id;
                        }
                        lanModel.Name = (GetRequest($"[{item.Id}].goodsName")).SafeString().Trim();
                        lanModel.AdvWord = (GetRequest($"[{item.Id}].advWord")).SafeString().Trim();
                        string oldPicName = lanModel.GoodsImage ?? "";
                        string newPicName = (GetRequest($"[{item.Id}].goodsImage")).SafeString().Trim();
                        if (oldPicName!=newPicName && !newPicName.IsNullOrEmpty())
                        {
                            GoodsImagesLan.SetMainPicture(oldPicName, newPicName, goodsCommon.Id, goods.StoreId, WorkingLanguage.Id);
                        }
                        lanModel.GoodsImage = (GetRequest($"[{item.Id}].goodsImage")).SafeString().Trim();
                        lanModel.GoodsVideoName = (GetRequest($"[{item.Id}].videoUrl")).SafeString().Trim();
                        lanModel.Content = (GetRequest($"goods_body_{item.Id}")).SafeString().Trim();
                        lanModel.MobileContent = (GetRequest($"goods_Mobilebody_{item.Id}")).SafeString().Trim();
                        lanModel.SeoTitle = (GetRequest($"[{item.Id}].seotitle")).SafeString().Trim();
                        lanModel.SeoKeys = (GetRequest($"[{item.Id}].seokeys")).SafeString().Trim();
                        lanModel.SeoDescription = (GetRequest($"[{item.Id}].seodescription")).SafeString().Trim();
                        lanModel.DeliveryTime = (GetRequest($"[{item.Id}].deliveryTime")).SafeString().Trim();
                        lanModel.DeliveryDate = (GetRequest($"[{item.Id}].deliveryDate")).SafeString().Trim();
                        lanModel.Save();
                    }
                }
                #endregion

                goods.Update();

                tran1.Commit();
            }
            // 清除缓存
            GoodsClassExAttributes.Meta.Cache.Clear("", true);
            GoodsExAttributes.Meta.Cache.Clear("", true);
            GoodsCommon.Meta.Cache.Clear("", true);
            Goods.Meta.Cache.Clear("", true);
            GoodsLan.Meta.Cache.Clear("", true);
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("Index") });
        }
        catch (Exception ex)
        {
            return Prompt(new PromptModel { Message = GetResource("添加商品过程中发生了错误：" + ex.Message) });
            throw;
        }
    }

    [DisplayName("编辑商品图片")]
    public IActionResult EditGoodsPicture(Int64 goodsId)
    {
        var userId = ManageProvider.User?.ID ?? 0;
        var model = Goods.FindById(goodsId);
        if (model == null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品不存在") });
        }

        // 检查是否有权限
        if (model.CreateUserID != userId)
        {
            return Prompt(new PromptModel { Message = GetResource("无权限") });
        }
        return View(model);
    }
    [HttpPost]
    [DisplayName("添加图片到图片表")]
    public IActionResult EditGoodsPicture(IFormCollection form)
    {
        try
        {
            Int64 commonId = form["commonid"].ToLong();
            if (commonId.IsNull()) return Json(new { success = false, message = "商品公共ID为空" });
            Int64 storeId = Store.FindByUId(ManageProvider.User?.ID ?? 0)?.Id??0;
            var goodsCommon = GoodsCommon.FindById(commonId);
            if(goodsCommon == null) return Json(new { success = false, message = "商品记录为空" });
            var goods = Goods.FindByGoodsCommonId(commonId);
            if(goods == null) return Json(new { success = false, message = "商品记录为空" });
            var images = new List<GoodsImages>();
            using (var tran = GoodsImages.Meta.CreateTrans())
            {
                // 遍历处理5张图片
                for (int i = 0; i < 5; i++)
                {
                    var fileId = form[$"img[0][{i}][Id]"];
                    var name = form[$"img[0][{i}][name]"].ToString();
                    if (name.IsNullOrEmpty()) continue;
                    var isdefault = form[$"img[0][{i}][default]"].ToInt();
                    var sort = form[$"img[0][{i}][sort]"].ToInt();  
                        //判断图片是否已经存在
                    var fileModel = GoodsImages.FindById(fileId.ToInt());
                    var gImg = new GoodsImages();
                    if (fileModel != null)
                    {
                        gImg = fileModel;
                    }

                    //创建信息已经添加过了，为了保持一致性
                    gImg.GoodsCommonId = commonId;
                    gImg.ImageUrl = name;
                    gImg.IsDefault = isdefault;
                    gImg.StoreId = storeId;
                    gImg.Sort = sort;
                    images.Add(gImg);
                    if (isdefault == 1 && name != goodsCommon.GoodsImage)//如果商品图片主图发生变化则修改
                    {
                        goodsCommon.GoodsImage = name;
                        goodsCommon.Update();
                        goods.GoodsImage = name;
                        goods.Update();
                    }
                }
                if (images.Count > 0)
                {
                    images.Save();
                }

                //图片翻译表
                var localizationSettings = LocalizationSettings.Current;
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var imageslan = new List<GoodsImagesLan>();

                if (localizationSettings.IsEnable)
                {
                    foreach (var item in Languagelist)
                    {
                        // 遍历处理5张图片
                        for (int i = 0; i < 5; i++)
                        {
                            var fileId = form[$"[{item.Id}].img[0][{i}][Id]"];
                            var name = form[$"[{item.Id}].img[0][{i}][name]"].ToString();
                            if (name.IsNullOrEmpty()) continue;
                            var isdefault = form[$"[{item.Id}].img[0][{i}][default]"].ToInt();
                            var sort = form[$"[{item.Id}].img[0][{i}][sort]"].ToInt();
                            //判断图片是否已经存在
                            var fileModel = GoodsImagesLan.FindById(fileId.ToInt());
                            var gImg = new GoodsImagesLan();
                            if (fileModel != null)
                            {
                                gImg = fileModel;
                            }
                            //创建信息已经添加过了，为了保持一致性
                            gImg.GoodsCommonId = commonId;
                            gImg.ImageUrl = name;
                            gImg.IsDefault = isdefault;
                            gImg.StoreId = storeId;
                            gImg.Sort = sort;
                            gImg.LId = item.Id;
                            imageslan.Add(gImg);
                            var goodslan = GoodsLan.FindByGIdAndLId(commonId, item.Id);
                            if (goodslan == null) continue;

                            if (isdefault == 1 && name != goodslan.GoodsImage)//如果商品图片主图发生变化则修改
                            {
                                if (name != goodslan.GoodsImage)
                                {
                                    goodslan.GoodsImage = name;
                                    goodslan.Update();
                                }

                            }
                        }
                        if (imageslan.Count > 0)
                        {
                            imageslan.Save();
                        }
                    }
                }
                tran.Commit();
            }
            dynamic viewModel = new ExpandoObject();
            viewModel.goodscommon = goodsCommon;// 清除缓存
            Goods.Meta.Cache.Clear("", true);
            GoodsLan.Meta.Cache.Clear("", true);
            GoodsCommon.Meta.Cache.Clear("", true);
            GoodsImages.Meta.Cache.Clear("", true);
            GoodsImagesLan.Meta.Cache.Clear("", true);
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("EditGoodsDetail",new {id = goods.Id}) });
        }
        catch (Exception ex)
        {
            return Prompt(new PromptModel { Message = GetResource("上传图片过程中发生错误：" + ex.Message) });
        }
    }
    public IActionResult EditGoodsSku(long goodsId)
    {
        if (goodsId.IsNull())
        {
            return Prompt(new PromptModel { Message = GetResource("商品ID无效") });
        }
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品不存在") });
        }
        dynamic viewModel = new ExpandoObject();
        viewModel.goods = goods;
        viewModel.goodsId = goods.Id.SafeString();
        var specValueList = GoodsSpecValue.FindAllByGoodsId(goodsId);
        viewModel.specValueList = specValueList;
        viewModel.specList = GoodsSpecValue.FindAllByParentIds("0");
        return View(viewModel);
    }
    public IActionResult SaveGoodsSku(long goodsId)
    {
        if (goodsId.IsNull())
        {
            return Prompt(new PromptModel { Message = GetResource("商品ID无效") });
        }
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品不存在") });
        }
        try
        {
            #region 商品SKU
            string materialIds = "";
            if (!GetRequest($"skucount").IsNullOrEmpty())
            {
                using (var tran = GoodsSKUDetail.Meta.CreateTrans())
                {
                    List<GoodsSKUDetail> skuList = new List<GoodsSKUDetail>();
                    var skucount = Convert.ToInt32((GetRequest($"skucount")).SafeString().Trim());
                    for (int i = 0; i < skucount; i++)
                    {
                        int specCount = Convert.ToInt32((GetRequest($"specValueCount")).SafeString().Trim());

                        Int64 skuId = (GetRequest($"spec[{i}][id]")).SafeString().Trim().IsNullOrEmpty() ? 0 : Convert.ToInt64((GetRequest($"spec[{i}][id]")).SafeString().Trim());
                        List<GoodsSpecDto> specDtos = new();
                        for (int j = 0; j < specCount; j++)
                        {
                            var specId = Convert.ToInt32((GetRequest($"spec[{i}][sp_value][{j}]")).SafeString().Trim());
                            var spec = GoodsSpecValue.FindById(specId);
                            if (spec == null) continue;
                            specDtos.Add(new GoodsSpecDto()
                            {
                                Id = spec.ParentId,
                                Name = spec.ParentName ?? "",
                                VId = spec.Id,
                                Value = spec.Name ?? "",
                            });
                        }
                        var sku = GoodsSKUDetail.FindById(skuId);
                        if (sku == null)
                        {
                            sku = new GoodsSKUDetail();
                            sku.SpecValue = specDtos.ToJson();
                            sku.GoodsId = goods.Id;
                        }
                        long materialId = Convert.ToInt64((GetRequest($"spec[{i}][material]")).SafeString().Trim());
                        var material = MerchantMaterial.FindById(materialId);
                        if (material == null)
                        {
                            return Prompt(new PromptModel { Message = GetResource("物料不存在") });
                        }
                        sku.MaterialId = materialId;
                        sku.GoodsMarketPrice = Convert.ToDecimal((GetRequest($"spec[{i}][marketprice]")).SafeString().Trim());
                        sku.GoodsPrice = Convert.ToDecimal((GetRequest($"spec[{i}][price]")).SafeString().Trim());

                        sku.Save();
                        skuList.Add(sku);

                        #region 阶梯价格
                        var tiereddata = GetRequest($"tiered[{i}][data]").SafeString();
                        if (!tiereddata.IsNullOrEmpty())
                        {
                            var tieredlist = JsonConvert.DeserializeObject<List<TieredPriceDto>>(tiereddata);
                            if (tieredlist != null && tieredlist?.Count > 0)
                            {
                                foreach (var item in tieredlist)
                                {
                                    var goodstieredPrice = new GoodsTieredPrice()
                                    {
                                        GoodsId = goods.Id,
                                        SkuId = sku.Id,
                                        StoreId = goods.StoreId,
                                        MinQuantity = item.MinQuantity,
                                        MaxQuantity = item.MaxQuantity,
                                        OriginalPrice = sku.GoodsPrice,
                                        Price = item.Price,
                                        Enabled = true
                                    };
                                    goodstieredPrice.Insert();
                                }
                            }

                        }
                        #endregion

                        materialIds += sku.MaterialId + ",";

                    }
                    materialIds = materialIds.SubString(0, materialIds.Length - 1);
                    string skuids = skuList.Select(x => x.Id).Distinct().Join(",");
                    GoodsSKUDetail.DeleteOther(goods.Id, skuids);
                    goods.MaterialIds = materialIds;
                    goods.Update();
                    Goods.Meta.Cache.Clear("", true);
                    tran.Commit();
                }
            }
            #endregion
            return Prompt(new PromptModel { Message = GetResource("操作成功"), IsOk = true, BackUrl = Url.Action("EditGoodsDetail", new { id = goods.Id }) });

        }
        catch (Exception ex)
        {
            return Prompt(new PromptModel { Message = GetResource("操作失败") +"，"+ GetResource("异常")+"："+ex.Message, IsOk = false });
        }


    }

    /// <summary>
    /// 上传资料
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    public IActionResult UploadData(Int64 Id)
    {
        var model = Goods.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("商品信息不存在"));
        }
        return View(model);
    }

    [HttpPost]
    public IActionResult UploadData(Int64 Id,String specificationFileName,String manualPdfName)
    {
        DResult res = new();
        var model = Goods.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("商品信息不存在");
            return Json(res);
        }
        try
        {
            model.Specification = specificationFileName;
            model.ProductManual = manualPdfName;
            model.Update();

            if (LocalizationSettings.Current.IsEnable)
            {
                var LanguageList = Language.FindByStatus().OrderBy(e => e.DisplayOrder);
                foreach (var item in LanguageList)
                {
                    var lan = GoodsLan.FindByGIdAndLId(model.Id, item.Id);
                    if(lan != null)
                    {
                        lan.Specification = GetRequest($"specificationFileName_{item.Id}").SafeString().Trim();
                        lan.ProductManual = GetRequest($"manualPdfName_{item.Id}").SafeString().Trim();
                        lan.Update();
                    }
                }
            }
            res.success = true;
            return Json(res);
        }
        catch (Exception ex)
        {
            res.msg = ex.ToString();
            return Json(res);
        }
    }

    /// <summary>
    /// 上传商品规格文件
    /// </summary>
    /// <param name="flie"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult UploadSpecification(IFormFile file)
    {
        var bytes = file.OpenReadStream().ReadBytes(file.Length);
        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
        var filepath = $"Goods/Specification/{filename}";
        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
        filepath = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
        saveFileName.EnsureDirectory();
        file.SaveAs(saveFileName);
        var Url = filepath.Replace("\\", "/");
        return Json(new { success = true, fileName = Url });
    }

    /// <summary>
    /// 上传商品手册PDF文件
    /// </summary>
    /// <param name="flie"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult UploadManualPdf(IFormFile file)
    {
        var bytes = file.OpenReadStream().ReadBytes(file.Length);
        var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(file.FileName)}";
        var filepath = $"Goods/ManualPdf/{filename}";
        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
        filepath = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
        saveFileName.EnsureDirectory();
        file.SaveAs(saveFileName);
        var Url = filepath.Replace("\\", "/");
        return Json(new { success = true, fileName = Url });
    }

    /// <summary>
    /// 设置SKU阶梯价格
    /// </summary>
    /// <returns></returns>
    public IActionResult TieredPriceView(long goodsId,long skuId,decimal price)
    {
        if (goodsId.IsNull())
        {
            return Json(new { success = false, message = GetResource("商品记录不存在") });
        }
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            return Json(new { success = false, message = GetResource("商品记录不存在") });
        }
        ViewBag.goods = goods;
        ViewBag.price = price;
        var skumodel = GoodsSKUDetail.FindById(skuId);
        if (!skumodel.IsNull())//如果有设SKUID
        {
            ViewBag.IsAdd = 0; //0表示编辑
            ViewBag.SkuId = skuId;
            ViewBag.tieredPrices = GoodsTieredPrice.FindAllBySkuId(skuId);
            return View();
        }
        ViewBag.IsAdd = 1; //1表示添加
        return View();
    }

    /// <summary>
    /// 有SKU属性保存阶梯价格，没有则返回数据
    /// </summary>
    /// <param name="goodsId"></param>
    /// <param name="skuId"></param>
    /// <param name="tieredPrice"></param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult SaveTieredPrice(long goodsId, long skuId, List<TieredPriceInput> tieredPrice)
    {
        if (goodsId.IsNull())
        {
            return Json(new DResult { success = false, msg = GetResource("商品记录不存在") });
        }
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            return Json(new DResult { success = false, msg = GetResource("商品记录不存在") });
        }
        var skuModel = GoodsSKUDetail.FindById(skuId);

        if (tieredPrice == null || tieredPrice.Count == 0)
        {
            // 如果前端没有提交阶梯价格，则删除所有现有记录
            if (!skuModel.IsNull())
            {
                var existingTieredPrices = GoodsTieredPrice.FindAllBySkuId(skuId);
                if (existingTieredPrices.Count > 0)
                {
                    existingTieredPrices.Delete();
                }
            }
            return Json(new DResult { success = false, msg = GetResource("没有有效的阶梯价格设置") });
        }

        // 按MinQuantity排序以确保正确验证
        tieredPrice = tieredPrice.OrderBy(t => t.MinQuantity).ToList();

        var tieredPriceList = new List<GoodsTieredPrice>();
        foreach (var tier in tieredPrice.Select((value, index) => new { value, index }))
        {
            var tieredPriceItem = new GoodsTieredPrice
            {
                GoodsId = goodsId,
                SkuId = skuId,
                StoreId = goods.StoreId,
                MinQuantity = tier.value.MinQuantity,
                MaxQuantity = (tier.index == tieredPrice.Count - 1) ? 0 : tier.value.MaxQuantity ?? 0,
                OriginalPrice = tier.value.OriginalPrice,
                Price = tier.value.Price,
                Enabled = true
            };

            var validateResult = ValidateTieredPrice(tieredPriceItem, tier.index, tieredPrice);
            if (!validateResult.success) return Json(validateResult);

            tieredPriceList.Add(tieredPriceItem);
        }

        var overlapCheck = CheckOverlapRanges(tieredPriceList);
        if (!overlapCheck.success) return Json(overlapCheck);

        // 删除现有记录（如果是修改）
        if (!skuModel.IsNull())
        {
            var existingTieredPrices = GoodsTieredPrice.FindAllBySkuId(skuId);
            if (existingTieredPrices.Count > 0)
            {
                existingTieredPrices.Delete();
            }
            tieredPriceList.Insert();
            GoodsTieredPrice.Meta.Cache.Clear("", true);
            return Json(new DResult { success = true, msg = GetResource("阶梯价格设置成功")});
        }

        if (tieredPriceList.Count > 0)//新增返回记录
        {
            var list = tieredPriceList.Select(e => new TieredPriceDto()
            {
                MinQuantity = e.MinQuantity,
                MaxQuantity = e.MaxQuantity,
                Price = e.Price
            });
            return Json(new DResult { success = true, msg = GetResource("阶梯价格设置成功"), data = list });
        }

        return Json(new DResult { success = false, msg = GetResource("没有有效的阶梯价格设置") });
    }

    /// <summary>
    /// 设置SKU阶梯价格
    /// </summary>
    /// <param name="skuId"></param>
    /// <summary>
    /// 验证阶梯价格数据
    /// </summary>
    private DResult ValidateTieredPrice(GoodsTieredPrice tier,  int index, List<TieredPriceInput> tieredPrice)
    {

        // 跳过无效条目
        if (tier.MinQuantity <= 0)
        {
            return new DResult { success = false, msg = GetResource("最小数量必须大于0") };
        }

        // 验证售价是否填写
        if (tier.Price <= 0)
        {
            return new DResult { success = false, msg = GetResource("阶梯价格区间必须设置有效的售价") };
        }

        // 确保MaxQuantity大于MinQuantity
        if (tier.MaxQuantity > 0 && tier.MaxQuantity <= tier.MinQuantity)
        {
            return new DResult { success = false, msg = GetResource("阶梯价格区间错误：最大购买数量必须大于最小购买数量") };
        }

        // 检查阶梯价格之间的连续性
        if (index > 0)
        {
            var prevTier = tieredPrice[index - 1];
            if (prevTier.MaxQuantity.HasValue && prevTier.MaxQuantity.Value > 0 &&
                prevTier.MaxQuantity.Value + 1 != tier.MinQuantity)
            {
                return new DResult { success = false, msg = GetResource("阶梯价格区间错误：价格区间之间不能有断层") };
            }
        }

        return new DResult { success = true };
    }

    /// <summary>
    /// 检查价格区间是否重叠
    /// </summary>
    private DResult CheckOverlapRanges(List<GoodsTieredPrice> tieredPriceList)
    {
        for (int i = 0; i < tieredPriceList.Count; i++)
        {
            for (int j = i + 1; j < tieredPriceList.Count; j++)
            {
                var tier1 = tieredPriceList[i];
                var tier2 = tieredPriceList[j];

                if ((tier1.MaxQuantity == 0 || tier2.MinQuantity < tier1.MaxQuantity) &&
                    (tier2.MaxQuantity == 0 || tier1.MinQuantity <= tier2.MaxQuantity))
                {
                    return new DResult { success = false, msg = GetResource("阶梯价格区间错误：价格区间不能重叠") };
                }
            }
        }
        return new DResult { success = true };
    }

    public class TieredPriceInput
    {
        public int MinQuantity { get; set; }
        public int? MaxQuantity { get; set; }
        public decimal OriginalPrice { get; set; }
        public decimal Price { get; set; }
    }
}