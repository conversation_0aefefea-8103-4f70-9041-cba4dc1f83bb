﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;

namespace B2B2CShop.Entity;

/// <summary>意见反馈</summary>
[Serializable]
[DataObject]
[Description("意见反馈")]
[BindIndex("IX_DH_FeedBack_UId", false, "UId")]
[BindTable("DH_FeedBack", Description = "意见反馈", ConnName = "Pek", DbType = DatabaseType.None)]
public partial class FeedBack : IFeedBack, IEntity<IFeedBack>
{
    #region 属性
    private Int32 _Id;
    /// <summary>编号</summary>
    [DisplayName("编号")]
    [Description("编号")]
    [DataObjectField(true, true, false, 0)]
    [BindColumn("Id", "编号", "")]
    public Int32 Id { get => _Id; set { if (OnPropertyChanging("Id", value)) { _Id = value; OnPropertyChanged("Id"); } } }

    private String? _Name;
    /// <summary>姓名</summary>
    [DisplayName("姓名")]
    [Description("姓名")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Name", "姓名", "", Master = true)]
    public String? Name { get => _Name; set { if (OnPropertyChanging("Name", value)) { _Name = value; OnPropertyChanged("Name"); } } }

    private String? _Phone;
    /// <summary>电话</summary>
    [DisplayName("电话")]
    [Description("电话")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Phone", "电话", "")]
    public String? Phone { get => _Phone; set { if (OnPropertyChanging("Phone", value)) { _Phone = value; OnPropertyChanged("Phone"); } } }

    private String? _Mail;
    /// <summary>邮箱</summary>
    [DisplayName("邮箱")]
    [Description("邮箱")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Mail", "邮箱", "")]
    public String? Mail { get => _Mail; set { if (OnPropertyChanging("Mail", value)) { _Mail = value; OnPropertyChanged("Mail"); } } }

    private String? _CompanyName;
    /// <summary>公司名称</summary>
    [DisplayName("公司名称")]
    [Description("公司名称")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CompanyName", "公司名称", "")]
    public String? CompanyName { get => _CompanyName; set { if (OnPropertyChanging("CompanyName", value)) { _CompanyName = value; OnPropertyChanged("CompanyName"); } } }

    private String? _Theme;
    /// <summary>主题</summary>
    [DisplayName("主题")]
    [Description("主题")]
    [DataObjectField(false, false, true, 200)]
    [BindColumn("Theme", "主题", "")]
    public String? Theme { get => _Theme; set { if (OnPropertyChanging("Theme", value)) { _Theme = value; OnPropertyChanged("Theme"); } } }

    private String? _Country;
    /// <summary>国家</summary>
    [DisplayName("国家")]
    [Description("国家")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("Country", "国家", "")]
    public String? Country { get => _Country; set { if (OnPropertyChanging("Country", value)) { _Country = value; OnPropertyChanged("Country"); } } }

    private String? _Content;
    /// <summary>反馈内容</summary>
    [DisplayName("反馈内容")]
    [Description("反馈内容")]
    [DataObjectField(false, false, true, 500)]
    [BindColumn("Content", "反馈内容", "")]
    public String? Content { get => _Content; set { if (OnPropertyChanging("Content", value)) { _Content = value; OnPropertyChanged("Content"); } } }

    private Int16 _FType;
    /// <summary>1:手机端 2:PC端</summary>
    [DisplayName("1")]
    [Description("1:手机端 2:PC端")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("FType", "1:手机端 2:PC端", "")]
    public Int16 FType { get => _FType; set { if (OnPropertyChanging("FType", value)) { _FType = value; OnPropertyChanged("FType"); } } }

    private Int32 _UId;
    /// <summary>会员ID</summary>
    [DisplayName("会员ID")]
    [Description("会员ID")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("UId", "会员ID", "")]
    public Int32 UId { get => _UId; set { if (OnPropertyChanging("UId", value)) { _UId = value; OnPropertyChanged("UId"); } } }

    private String _UName = null!;
    /// <summary>会员名称</summary>
    [DisplayName("会员名称")]
    [Description("会员名称")]
    [DataObjectField(false, false, false, 100)]
    [BindColumn("UName", "会员名称", "")]
    public String UName { get => _UName; set { if (OnPropertyChanging("UName", value)) { _UName = value; OnPropertyChanged("UName"); } } }

    private String? _CreateUser;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateUser", "创建者", "")]
    public String? CreateUser { get => _CreateUser; set { if (OnPropertyChanging("CreateUser", value)) { _CreateUser = value; OnPropertyChanged("CreateUser"); } } }

    private Int32 _CreateUserID;
    /// <summary>创建者</summary>
    [DisplayName("创建者")]
    [Description("创建者")]
    [DataObjectField(false, false, false, 0)]
    [BindColumn("CreateUserID", "创建者", "")]
    public Int32 CreateUserID { get => _CreateUserID; set { if (OnPropertyChanging("CreateUserID", value)) { _CreateUserID = value; OnPropertyChanged("CreateUserID"); } } }

    private DateTime _CreateTime;
    /// <summary>创建时间</summary>
    [DisplayName("创建时间")]
    [Description("创建时间")]
    [DataObjectField(false, false, true, 0)]
    [BindColumn("CreateTime", "创建时间", "")]
    public DateTime CreateTime { get => _CreateTime; set { if (OnPropertyChanging("CreateTime", value)) { _CreateTime = value; OnPropertyChanged("CreateTime"); } } }

    private String? _CreateIP;
    /// <summary>创建地址</summary>
    [DisplayName("创建地址")]
    [Description("创建地址")]
    [DataObjectField(false, false, true, 50)]
    [BindColumn("CreateIP", "创建地址", "")]
    public String? CreateIP { get => _CreateIP; set { if (OnPropertyChanging("CreateIP", value)) { _CreateIP = value; OnPropertyChanged("CreateIP"); } } }
    #endregion

    #region 拷贝
    /// <summary>拷贝模型对象</summary>
    /// <param name="model">模型</param>
    public void Copy(IFeedBack model)
    {
        Id = model.Id;
        Name = model.Name;
        Phone = model.Phone;
        Mail = model.Mail;
        CompanyName = model.CompanyName;
        Theme = model.Theme;
        Country = model.Country;
        Content = model.Content;
        FType = model.FType;
        UId = model.UId;
        UName = model.UName;
        CreateUser = model.CreateUser;
        CreateUserID = model.CreateUserID;
        CreateTime = model.CreateTime;
        CreateIP = model.CreateIP;
    }
    #endregion

    #region 获取/设置 字段值
    /// <summary>获取/设置 字段值</summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public override Object? this[String name]
    {
        get => name switch
        {
            "Id" => _Id,
            "Name" => _Name,
            "Phone" => _Phone,
            "Mail" => _Mail,
            "CompanyName" => _CompanyName,
            "Theme" => _Theme,
            "Country" => _Country,
            "Content" => _Content,
            "FType" => _FType,
            "UId" => _UId,
            "UName" => _UName,
            "CreateUser" => _CreateUser,
            "CreateUserID" => _CreateUserID,
            "CreateTime" => _CreateTime,
            "CreateIP" => _CreateIP,
            _ => base[name]
        };
        set
        {
            switch (name)
            {
                case "Id": _Id = value.ToInt(); break;
                case "Name": _Name = Convert.ToString(value); break;
                case "Phone": _Phone = Convert.ToString(value); break;
                case "Mail": _Mail = Convert.ToString(value); break;
                case "CompanyName": _CompanyName = Convert.ToString(value); break;
                case "Theme": _Theme = Convert.ToString(value); break;
                case "Country": _Country = Convert.ToString(value); break;
                case "Content": _Content = Convert.ToString(value); break;
                case "FType": _FType = Convert.ToInt16(value); break;
                case "UId": _UId = value.ToInt(); break;
                case "UName": _UName = Convert.ToString(value); break;
                case "CreateUser": _CreateUser = Convert.ToString(value); break;
                case "CreateUserID": _CreateUserID = value.ToInt(); break;
                case "CreateTime": _CreateTime = value.ToDateTime(); break;
                case "CreateIP": _CreateIP = Convert.ToString(value); break;
                default: base[name] = value; break;
            }
        }
    }
    #endregion

    #region 关联映射
    #endregion

    #region 扩展查询
    /// <summary>根据编号查找</summary>
    /// <param name="id">编号</param>
    /// <returns>实体对象</returns>
    public static FeedBack? FindById(Int32 id)
    {
        if (id < 0) return null;

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.Find(e => e.Id == id);

        // 单对象缓存
        return Meta.SingleCache[id];

        //return Find(_.Id == id);
    }

    /// <summary>根据会员ID查找</summary>
    /// <param name="uId">会员ID</param>
    /// <returns>实体列表</returns>
    public static IList<FeedBack> FindAllByUId(Int32 uId)
    {
        if (uId < 0) return [];

        // 实体缓存
        if (Meta.Session.Count < 1000) return Meta.Cache.FindAll(e => e.UId == uId);

        return FindAll(_.UId == uId);
    }
    #endregion

    #region 字段名
    /// <summary>取得意见反馈字段信息的快捷方式</summary>
    public partial class _
    {
        /// <summary>编号</summary>
        public static readonly Field Id = FindByName("Id");

        /// <summary>姓名</summary>
        public static readonly Field Name = FindByName("Name");

        /// <summary>电话</summary>
        public static readonly Field Phone = FindByName("Phone");

        /// <summary>邮箱</summary>
        public static readonly Field Mail = FindByName("Mail");

        /// <summary>公司名称</summary>
        public static readonly Field CompanyName = FindByName("CompanyName");

        /// <summary>主题</summary>
        public static readonly Field Theme = FindByName("Theme");

        /// <summary>国家</summary>
        public static readonly Field Country = FindByName("Country");

        /// <summary>反馈内容</summary>
        public static readonly Field Content = FindByName("Content");

        /// <summary>1:手机端 2:PC端</summary>
        public static readonly Field FType = FindByName("FType");

        /// <summary>会员ID</summary>
        public static readonly Field UId = FindByName("UId");

        /// <summary>会员名称</summary>
        public static readonly Field UName = FindByName("UName");

        /// <summary>创建者</summary>
        public static readonly Field CreateUser = FindByName("CreateUser");

        /// <summary>创建者</summary>
        public static readonly Field CreateUserID = FindByName("CreateUserID");

        /// <summary>创建时间</summary>
        public static readonly Field CreateTime = FindByName("CreateTime");

        /// <summary>创建地址</summary>
        public static readonly Field CreateIP = FindByName("CreateIP");

        static Field FindByName(String name) => Meta.Table.FindByName(name);
    }

    /// <summary>取得意见反馈字段名称的快捷方式</summary>
    public partial class __
    {
        /// <summary>编号</summary>
        public const String Id = "Id";

        /// <summary>姓名</summary>
        public const String Name = "Name";

        /// <summary>电话</summary>
        public const String Phone = "Phone";

        /// <summary>邮箱</summary>
        public const String Mail = "Mail";

        /// <summary>公司名称</summary>
        public const String CompanyName = "CompanyName";

        /// <summary>主题</summary>
        public const String Theme = "Theme";

        /// <summary>国家</summary>
        public const String Country = "Country";

        /// <summary>反馈内容</summary>
        public const String Content = "Content";

        /// <summary>1:手机端 2:PC端</summary>
        public const String FType = "FType";

        /// <summary>会员ID</summary>
        public const String UId = "UId";

        /// <summary>会员名称</summary>
        public const String UName = "UName";

        /// <summary>创建者</summary>
        public const String CreateUser = "CreateUser";

        /// <summary>创建者</summary>
        public const String CreateUserID = "CreateUserID";

        /// <summary>创建时间</summary>
        public const String CreateTime = "CreateTime";

        /// <summary>创建地址</summary>
        public const String CreateIP = "CreateIP";
    }
    #endregion
}
