﻿@using B2B2CShop.Entity
@{
    Layout = "_RootNoHeader";
    var dfaultPrice = ViewBag.price;
}
<link rel="stylesheet" href="/static/home/<USER>/seller.css">

<style>
    .text{
        width:60px;
    }
    .tiered-price-table {
        margin: 0 auto;
        text-align: center;
    }
</style>

<div class="p20" style="margin: 10px;">
    <form id="specForm" method="post"  action="@Url.Action("SaveTieredPrice")">
        <input type="hidden" name="goodsId" value="@ViewBag.goods.Id" />
        <dl>
            <dt>@T("阶梯价格")：</dt>
            <dd>
                <div id="tieredPriceContainer" style="margin:5px;">
                    <table class="tiered-price-table" style=" margin-bottom:5px;">
                        <thead>
                            <tr>
                                <th style="width:10%">@T("最小购买数量")</th>
                                <th style="width:10%">@T("最大购买数量")</th>
                                <th style="width:10%">@T("原价")</th>
                                <th style="width:10%">@T("售价")</th>
                                <th style="width:10%"></th>
                            </tr>
                        </thead>
                        @if (ViewBag.IsAdd==1)//新增
                        {
                            <tbody id="tieredPriceRows">
                                <tr class="tiered-price-row">
                                    <td style="width:10%"><input name="tieredPrice[0].MinQuantity" type="number" min="1" class="text" value="1"/></td>
                                    <td style="width:10%"><input name="tieredPrice[0].MaxQuantity" type="number" min="2" class="text" /></td>
                                    <td style="width:10%">
                                        <input name="tieredPrice[0].OriginalPrice" min="0" type="number" step="any" class="text" value="@dfaultPrice" readonly style="background:#E7E7E7" />
                                    </td>
                                    <td style="width:10%"><input name="tieredPrice[0].Price" type="number" min="0" step="any" class="text" value="@dfaultPrice" required /></td>
                                    <td style="width:10%"><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
                                </tr>
                            </tbody>
                        }
                        else//编辑
                        {
                            <input type="hidden" name="skuId" value="@ViewBag.SkuId" />
                            <tbody id="tieredPriceRows">
                                @if (ViewBag.tieredPrices.Count==0)
                                {
                                    <tr class="tiered-price-row">
                                        <td style="width:10%"><input name="tieredPrice[0].MinQuantity" type="number" min="1" class="text" value="1" /></td>
                                        <td style="width:10%"><input name="tieredPrice[0].MaxQuantity" type="number" min="2" class="text" /></td>
                                        <td style="width:10%">
                                            <input name="tieredPrice[0].OriginalPrice" min="0" type="number" step="any" class="text" value="@dfaultPrice" readonly style="background:#E7E7E7" />
                                        </td>
                                        <td style="width:10%"><input name="tieredPrice[0].Price" type="number" min="0" step="any" value="@dfaultPrice" class="text" required /></td>
                                        <td style="width:10%"><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
                                    </tr>
                                }
                                @for(int i = 0;i<ViewBag.tieredPrices.Count;i++)
                                {
                                    GoodsTieredPrice item = ViewBag.tieredPrices[i];
                                    <tr class="tiered-price-row">
                                        <td style="width:10%">
                                            <input name="tieredPrice[@i].MinQuantity" type="number" min="1" class="text" value="@item.MinQuantity" />
                                        </td>
                                        <td style="width:10%">
                                            <input name="tieredPrice[@i].MaxQuantity" type="number" min="2" class="text" value="@(item.MaxQuantity == 0 ? "" : item.MaxQuantity)" />
                                        </td>
                                        <td style="width:10%">
                                            <input name="tieredPrice[@i].OriginalPrice" min="0" type="number" step="any" class="text" value="@(item.OriginalPrice > 0 ? item.OriginalPrice : ViewBag.price)" />
                                        </td>
                                        <td style="width:10%">
                                            <input name="tieredPrice[@i].Price" type="number" min="0" step="any" class="text" value="@(item.Price > 0 ? item.Price : ViewBag.price)" required />
                                        </td>
                                        <td style="width:10%"><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
                                    </tr>
                                }
                            </tbody>
                        }

                    </table>
                    <a href="javascript:void(0);" id="addTieredPriceRow" class="dssc-btn"><i class="iconfont">&#xe733;</i>@T("添加阶梯价格")</a>
                </div>
                <p class="hint">@T("设置不同数量区间的商品价格，鼓励客户批量购买。")</p>
            </dd>
        </dl>
        <div class="bottom">
            <input type="button" class="submit" value="@T("提交")" id="submitBtn">
        </div>
    </form>
</div>

<script type="text/javascript">
    $(function() {
        // 提交按钮点击事件
        $('#submitBtn').click(function() {
            // 禁用按钮防止重复提交
            $(this).prop('disabled', true);
            
            // 收集表单数据
            var formData = $('#specForm').serialize();
            
            // 发送AJAX请求
            $.ajax({
                url: $('#specForm').attr('action'),
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    // 处理成功响应
                    if(response.success) {
                        layer.msg(response.msg || '操作成功');
                        // 调用父窗口的回调函数并传递数据
                        if(window.parent && window.parent.tieredPriceCallback) {
                            window.parent.tieredPriceCallback(response.data);
                        }
                        // 关闭当前弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    } else {
                        layer.msg(response.msg || '操作失败');
                    }
                    // 重新启用按钮
                    $('#submitBtn').prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    // 处理错误
                    layer.msg('请求失败: ' + error);
                    // 重新启用按钮
                    $('#submitBtn').prop('disabled', false);
                }
            });
        });
        
        // 初始化行计数器
        // 初始化行计数器
        var rowCount = 1;
        // 默认的最小购买数量
        var DEFAULT_MIN_QUANTITY = 1;

        // 页面加载时设置第一行的最小购买数量为默认值、原价为商品价格
        $(document).ready(function() {
            $("input[name='tieredPrice[0].MinQuantity']").val(DEFAULT_MIN_QUANTITY);

            // 如果商品价格已有值，则设置到原价字段
            var goodsPrice = $('input[name="price"]').val();
            if(goodsPrice) {
                $('input[name$=".OriginalPrice"]').val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');
            }
        });

        // 商品价格自动同步到阶梯价格的原价字段
        $('input[name="price"]').on('input change', function() {
            var goodsPrice = $(this).val();
            $('input[name$=".OriginalPrice"]').val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');
            $('input[name$="tieredPrice[0].Price"]').val(goodsPrice);
        });

        // 重置阶梯价格表格，清空所有行，重新添加首行
        function resetTieredPriceTable() {
            // 获取当前商品价格
            var goodsPrice = $('input[name="price"]').val() || '';

            // 清空表格
            $("#tieredPriceRows").empty();
            rowCount = 0;

            // 添加第一行，最小购买数量默认为2，原价设为商品价格且只读
            var firstRow = `
            <tr class="tiered-price-row">
                <td><input name="tieredPrice[0].MinQuantity" type="number" min="1" class="text w60" value="${DEFAULT_MIN_QUANTITY}" /></td>
                <td><input name="tieredPrice[0].MaxQuantity" type="number" min="2" class="text w60" /></td>
                <td><input name="tieredPrice[0].OriginalPrice" type="number" min="0" step="any" class="text w60" value="${@dfaultPrice}" readonly style="background:#E7E7E7" /></td>
                <td><input name="tieredPrice[0].Price" type="number" min="0" step="any" class="text w60" required/></td>
                <td><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
            </tr>`;

            $("#tieredPriceRows").append(firstRow);
            rowCount = 1;

            // 启用新行的验证
            setupTieredPriceValidation();
        }

        // 添加重置按钮到阶梯价格区域
        $("#tieredPriceContainer").append('<a href="javascript:void(0);" id="resetTieredPrice" class="dssc-btn" style="margin-left:10px;"><i class="iconfont">&#xe67a;</i>重置阶梯价格</a>');

        // 为重置按钮绑定点击事件
        $("#resetTieredPrice").click(function() {
            // 询问用户是否确定重置
            layer.confirm('确定要重置所有阶梯价格规则吗？', {
                btn: ['确定','取消']
            }, function(){
                resetTieredPriceTable();
                layer.msg('阶梯价格已重置');
            });
        });

        // 添加新行时也要考虑到原价同步
        $("#addTieredPriceRow").click(function() {
            // 获取当前商品价格
            var goodsPrice = $('input[name="price"]').val() || '';

            // 如果表格中没有行，先添加一行
            if ($("#tieredPriceRows tr").length === 0) {
                resetTieredPriceTable();
                return;
            }

            // 查找最后一行的最大数量
            var lastMaxQuantity = 0;
            if ($("#tieredPriceRows tr").length > 0) {
                var lastRow = $("#tieredPriceRows tr:last");
                var lastMaxInput = lastRow.find("input[name$='.MaxQuantity']");
                lastMaxQuantity = parseInt(lastMaxInput.val()) || 0;

                // 如果最后最大数量为0（无限），则提醒用户
                if (lastMaxQuantity === 0) {
                    layer.msg("上一个价格区间的最大数量为不限，不能再添加价格区间");
                    return;
                }
            }
            rowCount = $("#tieredPriceRows tr").length;

            var newRow = `
            <tr class="tiered-price-row">
                <td><input name="tieredPrice[${rowCount}].MinQuantity" type="number" min="1" class="text w60" value="${lastMaxQuantity > 0 ? (lastMaxQuantity + 1) :     DEFAULT_MIN_QUANTITY}" ${lastMaxQuantity > 0 ? 'readonly' : ''} /></td>
                <td><input name="tieredPrice[${rowCount}].MaxQuantity" type="number" min="${lastMaxQuantity+2}" class="text w60" /></td>
                <td><input name="tieredPrice[${rowCount}].OriginalPrice" type="number" class="text w60" step="any" value="${@dfaultPrice}" readonly style="background:#E7E7E7" /></td>
                <td><input name="tieredPrice[${rowCount}].Price" type="number" step="any" class="text w60" required/></td>
                <td><a href="javascript:void(0);" class="dssc-btn-mini remove-row"><i class="iconfont">&#xe67a;</i></a></td>
            </tr>`;

            $("#tieredPriceRows").append(newRow);
            // rowCount++;

            // 启用新行的验证
            setupTieredPriceValidation();
        });

        // 删除分层价格行
        $(document).on("click", ".remove-row", function() {
            var currentRow = $(this).closest("tr");
            var nextRow = currentRow.next("tr.tiered-price-row");
            var prevRow = currentRow.prev("tr.tiered-price-row");

            // 如果这是最后一行，则不允许删除，而是重置
            if ($("#tieredPriceRows tr").length === 1) {
                var firstRowMinInput = currentRow.find("input[name$='.MinQuantity']");
                firstRowMinInput.val(DEFAULT_MIN_QUANTITY);
                firstRowMinInput.prop('readonly', false);

                // 清空其他输入
                currentRow.find("input[name$='.MaxQuantity']").val("");
                currentRow.find("input[name$='.Price']").val("");

                // 获取当前商品价格
                var goodsPrice = $('input[name="price"]').val() || '';
                currentRow.find("input[name$='.OriginalPrice']").val(goodsPrice).prop('readonly', true).css('background', '#E7E7E7');

                layer.msg("至少需要保留一行阶梯价格，已重置为默认值");
                return;
            }

            // 如果有下一行，需要更新其最小数量
            if (nextRow.length > 0) {
                // 如果有上一行，则使用上一行的最大数量+1作为下一行的最小数量
                if (prevRow.length > 0) {
                    var prevMaxQuantity = parseInt(prevRow.find("input[name$='.MaxQuantity']").val()) || 0;
                    if (prevMaxQuantity > 0) {
                        var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                        nextMinInput.val(prevMaxQuantity + 1);
                        nextMinInput.prop('readonly', true);
                    }
                } else {
                    // 如果没有上一行，则下一行的最小数量设为默认值
                    var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                    nextMinInput.val(DEFAULT_MIN_QUANTITY);
                    nextMinInput.prop('readonly', false);
                }
            }

            // 删除当前行
            currentRow.remove();

            // 重新计算行索引
            recalculateRowIndices();
        });

        // 分层价格输入的验证设置
        function setupTieredPriceValidation() {
            // 当最大数量输入变化时的处理
            $("#tieredPriceRows").on("change", "input[name$='.MaxQuantity']", function() {
                var currentRow = $(this).closest("tr");
                var currentMinInput = currentRow.find("input[name$='.MinQuantity']");
                var currentMaxInput = $(this);
                var nextRow = currentRow.next("tr.tiered-price-row");

                var minQuantity = parseInt(currentMinInput.val()) || 0;
                var maxQuantity = parseInt(currentMaxInput.val()) || 0;

                // 确保最大值大于最小值
                if (maxQuantity > 0 && maxQuantity <= minQuantity) {
                    layer.msg("最大购买数量必须大于最小购买数量");
                    currentMaxInput.val('');
                    return;
                }

                // 如果有下一行，则更新其最小数量为当前行的最大数量+1
                if (nextRow.length > 0 && maxQuantity > 0) {
                    var nextMinInput = nextRow.find("input[name$='.MinQuantity']");
                    nextMinInput.val(maxQuantity + 1);
                    nextMinInput.prop('readonly', true);
                }
            });

            // 当价格输入变化时的处理
            $("#tieredPriceRows").on("change", "input[name$='.Price'], input[name$='.OriginalPrice']", function() {
                var value = parseFloat($(this).val());
                if (isNaN(value) || value <= 0) {
                    layer.msg("价格必须是大于0的数字");
                    $(this).val('');
                }
            });
        }

        // 删除行后重新计算行索引
        function recalculateRowIndices() {
            $("#tieredPriceRows tr").each(function(index) {
                $(this).find("input").each(function() {
                    var name = $(this).attr("name");
                    if (name) {
                        name = name.replace(/\[\d+\]/, '[' + index + ']');
                        $(this).attr("name", name);
                    }
                });
            });

            // 更新行计数器
            rowCount = $("#tieredPriceRows tr").length;
        }
    });
</script>