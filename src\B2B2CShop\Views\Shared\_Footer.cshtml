﻿@using B2B2CShop.Entity
@{
    var companyHelpId = HelpsCategory.FindByName("公司")?.Id ?? 0;
    var companyHelps = Helps.FindAllByHId(companyHelpId);
    var supportHelpId = HelpsCategory.FindByName("支持")?.Id ?? 0;
    var supportHelps = Helps.FindAllByHId(supportHelpId);
}
<!-- 底部开始 -->
<div class="footer"> 
    <div class="footer1">
        <div class="one">
            <h3>@T("深圳市海凌科电子有限公司")</h3>
            <ul>
                <li>@T("电话：0755-23152658")</li>
                <li>@T("邮箱 : <EMAIL>")</li>
                <li>@T("地址 : 广东省深圳市龙华区民治街道 民乐社区星河WORLD E栋大厦17层 1705、1706、1709A")</li>
            </ul>
        </div>
        <div class="two">
            <h3>@HelpsCategoryLan.FindByHIdAndLId(companyHelpId, Language.CurrentLanguage.Id)?.RealName</h3>
            <ul>
                @foreach (var item in companyHelps)
                {
                    if (item.Url.IsNullOrEmpty())
                    {
                        <li><a href="/Help/Index?category=@HelpsCategoryLan.FindByHIdAndLId(item.HId, Language.CurrentLanguage.Id)?.RealName&item=@HelpsLan.FindByHIdAndLId(item.Id, Language.CurrentLanguage.Id)?.RealName">@HelpsLan.FindByHIdAndLId(item.Id, Language.CurrentLanguage.Id)?.RealName</a></li>
                    }
                    else
                    {
                        <li><a href="@item.Url">@HelpsLan.FindByHIdAndLId(item.Id, Language.CurrentLanguage.Id)?.RealName</a></li>
                    }
                }
            </ul>
        </div>
        @* <div class="three">
            <h3>@("资源")</h3>
            <ul>
                <li><a href="#">@("新品速递")</a></li>
                <li><a href="#">@T("代理招商")</a></li>
                <li><a href="#">@("应用场景")</a></li>
                <li><a href="#">@("服务和工具")</a></li>
            </ul>
        </div> *@
        <div class="four">
            <h3>@HelpsCategoryLan.FindByHIdAndLId(supportHelpId, Language.CurrentLanguage.Id)?.RealName</h3>
            <ul>
                @foreach (var item in supportHelps)
                {
                    if (item.Url.IsNullOrEmpty())
                    {
                        <li><a href="/Help/Index?category=@HelpsCategoryLan.FindByHIdAndLId(item.HId, Language.CurrentLanguage.Id)?.RealName&item=@HelpsLan.FindByHIdAndLId(item.Id, Language.CurrentLanguage.Id)?.RealName">@HelpsLan.FindByHIdAndLId(item.Id, Language.CurrentLanguage.Id)?.RealName</a></li>
                    }
                    else
                    {
                        <li><a href="@item.Url" target="_blank">@HelpsLan.FindByHIdAndLId(item.Id, Language.CurrentLanguage.Id)?.RealName</a></li>
                    }
                }
                <li><a href="/Help/FeedBack">@T("反馈")</a></li>
            </ul>
        </div>
        <div class="five">
            <h3>@T("联系我们")</h3>
            <div class="social-icons">
                <a href="#"></a>
                <a href="#"><img src="~/public/images/icons/youjian-baidi.png" alt="youjian"></a>
                <a href="#"><img src="~/public/images/icons/wangzhi-landi.png" alt="wangzhi"></a>
                <a href="#"><img src="~/public/images/icons/skype-baidi.png" alt="skype"></a>
            </div>
            <p></p>
        </div>
    </div>
    <div class="xline" style="background-color: var(--text-color1);margin-top: 10px;"></div>
    <div style="padding: 10px;text-align: center;color:#BBBBBB;">
        <a>@T("联系电话 : 0755-23152658")</a> &nbsp;&nbsp;&nbsp; 
        <a>@T("版权所有 : 深圳市海凌科电子有限公司")</a> &nbsp;&nbsp;&nbsp; 
        <a href="https://beian.miit.gov.cn/" target="_blank">@T("备案号 : 粤ICP备12055399号-22")</a> &nbsp;&nbsp;&nbsp;
        <style>
            .footer a[href]:not([href=""]):hover {
                text-decoration: underline;
            }
        </style>
    </div>
    
</div>
<!-- 底部结束 -->