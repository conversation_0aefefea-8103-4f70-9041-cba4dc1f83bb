﻿using B2B2CShop.Dto;
using B2B2CShop.Entity;
using DH.Core.Domain.Localization;
using DH.Entity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using NewLife;
using NewLife.Caching;
using NewLife.Data;
using NewLife.Log;
using NewLife.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.DsMallUI.Common;
using Pek.Helpers;
using Pek.Models;
using Pek.NCube;
using Pek.NCubeUI.Common;
using Pek.Webs;
using SixLabors.ImageSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.IO;
using System.Reflection;
using System.Xml.Linq;
using XCode;
using XCode.Membership;

namespace B2B2CShop.Areas.Sellers.Controllers;

/// <summary>
/// 商家中心商品添加控制器
/// </summary>
[SellersArea]
[SellersAuthorize]
public class SellerGoodsAddController : PekBaseControllerX
{
    public IActionResult Index(int stapleId)
    {
        dynamic viewModel = new ExpandoObject();
        var goodsClassList = GoodsClass.FindAllByParentId(0).Select(x => new GoodsClass
        {
            Id = x.Id,
            ParentId = x.ParentId,
            GoodTypeId = x.GoodTypeId,
            Name = GoodsClassLan.FindByGIdAndLId(x.Id, WorkingLanguage.Id)?.RealName ?? x.Name
        });
        var classstaple = GoodsClassStaple.FindById(stapleId);
        viewModel.classstaple = classstaple;
        viewModel.classStapleList = GoodsClassStaple.GetTop10List();
        viewModel.list = goodsClassList;
        viewModel.lId = WorkingLanguage.Id;
        return View(viewModel);
    }
    [DisplayName("添加商品分类")]
    public IActionResult CreateGoodsClass()
    {
        dynamic viewModel = new ExpandoObject();
        var goodsClassList = GoodsClass.FindAllByParentId(0).Select(x => new GoodsClass
        {
            Id = x.Id,
            ParentId = x.ParentId,
            GoodTypeId = x.GoodTypeId,
            Name = GoodsClassLan.FindByGIdAndLId(x.Id, WorkingLanguage.Id)?.RealName
        });
        viewModel.list = goodsClassList;
        viewModel.lId = WorkingLanguage.Id;
        return View(viewModel);
    }
    /// <summary>
    /// 打开图片相册
    /// </summary>
    /// <returns></returns>
    [DisplayName("图片相册")]
    public IActionResult GoodsAlbumPic(int demo)
    {
        dynamic viewModel = new ExpandoObject();
        string className = "select_submit";
        if (demo==1) className = "select_s";
        viewModel.submitName = className;
        viewModel.editdemo = demo;
        return PartialView(viewModel);
    }
    /// <summary>
    ///填写号商品分类ID跳转到的商品详情信息页面
    /// </summary>
    /// <param name="class_id"></param>
    /// <returns></returns>
    [DisplayName("添加商品信息")]
    public IActionResult CreateGoodsDetail(Int64 class_id)
    {
        if (class_id.IsNull()) return Prompt(new PromptModel { Message = GetResource("商品分类为空") });
        if (class_id<=0) return Prompt(new PromptModel { Message = GetResource("商品分类为空") });
        dynamic viewModel = new ExpandoObject();
        CreataGoodsClassStaple(class_id);
        var countryList = Country.FindAllWithCache().Select(x => new Country
        {
            Id = x.Id,
            TwoLetterIsoCode = x.TwoLetterIsoCode,
            Name = CountryLan.FindNameByCIdAndLId(x.Id, WorkingLanguage.Id)
        });
        viewModel.countryList = countryList;

        viewModel.classid = class_id;//商品分类Id

        viewModel.lId = WorkingLanguage.Id;

        return View(viewModel);
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="classId">商品分类ID</param>
    /// <param name="goodsName">商品名称</param>
    /// <param name="advWord">商品广告词</param>
    /// <param name="goodsPrice">商品单价</param>
    /// <param name="goodsMarketPrice">市场价</param>
    /// <param name="goodsCostPrice">成本价</param>
    /// <param name="goodsDiscount">折扣</param>
    /// <param name="merchantMaterial">商品物料编号</param>
    /// <param name="goodsSerial">商品货号</param>
    /// <param name="goodsImage">商品图片</param>
    /// <param name="videoUrl">视频</param>
    /// <param name="goodsBody">PC端描述</param>
    /// <param name="goods_Mobilebody">手机端描述</param>
    /// <param name="brandId">品牌ID</param>
    /// <param name="plateidTop">头部关联板式</param>
    /// <param name="plateidBottom">底部关联板式</param>
    /// <param name="goodsFreight">运费</param>
    /// <param name="goodsVat">是否开发票</param>
    /// <param name="goodsStcids">店铺分类</param>
    /// <param name="isGoodsFCode">是否是F码商品</param>
    /// <param name="isAppoint">是否预约商品</param>
    /// <param name="appointSatedate">预约出售时间</param>
    /// <param name="isVirtual">是否为虚拟商品 1:是 0:否</param>
    /// <param name="virtualType">虚拟商品类型 0:核销商品 1:卡券商品 2:网盘商品 3:下载商品</param>
    /// <param name="VirtualIndate">虚拟商品有效期</param>
    /// <param name="virtualLimit">虚拟商品购买上限</param>
    /// <param name="goodsShelftime">上架时间</param>
    /// <param name="goodsCommend">商品推荐 1:是 0:否</param>
    /// <returns></returns>
    //[EntityAuthorize(PermissionFlags.Insert)]
    [HttpPost]
    [DisplayName("添加商品信息并切换到添加图片页面上")]
    //商品分类、商品名称、商品卖点
    public IActionResult AddGoodsDetail(Int64 classId, String goodsName, String advWord, Decimal goodsPrice, Decimal goodsMarketPrice, Decimal goodsCostPrice, Double goodsDiscount, Int64 merchantMaterial, String goodsSerial, String goodsImage, String videoUrl, String goodsBody, String goods_Mobilebody, Int32? brandId, Int64 plateidTop, Int64 plateidBottom, Decimal goodsFreight, Int32 goodsVat, String goodsStcids, Int32 isGoodsFCode, Int32 isAppoint, Int64 appointSatedate, Int32? isVirtual, Int32? virtualType, Int64? VirtualIndate, Int32? virtualLimit, Int32 goodsState, Int64 goodsShelftime, Int32 goodsCommend, List<TieredPriceInput> tieredPrice, String seotitle, String seokeys, String seodescription) // Add tieredPrice parameter
    {
        if (classId.IsNull()) return Prompt(new PromptModel { Message = GetResource("商品分类为空") });
        if (goodsName.IsNullOrEmpty()) return Prompt(new PromptModel { Message = GetResource("商品名称为空") });
        if (goodsPrice.IsNull()) return Prompt(new PromptModel { Message = GetResource("商品价格为空") });
        if (merchantMaterial.IsNull()) return Prompt(new PromptModel { Message = GetResource("商品物料为空") });
        var goodsClass = GoodsClass.FindById(classId);
        if (goodsClass == null) return Prompt(new PromptModel { Message = GetResource("找不到商品分类数据") });
        var storeEntity = Store.FindByUId(ManageProvider.User?.ID ?? 0);
        if (storeEntity == null) return Prompt(new PromptModel { Message = GetResource("商家信息不存在") });
        try
        {
            var goodsCommon = new GoodsCommon();//添加商品公共表
            var goods = new Goods();//添加商品表

            using (var tran1 = GoodsCommon.Meta.CreateTrans())
            {
                #region 商品公共表
                goodsCommon.Name = goodsName;
                goodsCommon.AdvWord = advWord;
                //商品分类Id多级
                string[] classIds = goodsClass.ParentIdList.Split(',', StringSplitOptions.RemoveEmptyEntries);
                goodsCommon.CId = classId;
                goodsCommon.Cid1 = Int64.Parse(classIds[0]);
                goodsCommon.Cid2 = Int64.Parse(classIds[1]);
                if (classIds.Length > 2)
                {
                    goodsCommon.Cid3 = Int64.Parse(classIds[2]);
                }
                goodsCommon.GoodsClassName = goodsClass.Name;
                goodsCommon.StoreId = storeEntity.Id;
                goodsCommon.StoreName = storeEntity.Name;
                //goodsCommon.BrandId = brandId.Value;
                //goodsCommon.BrandName = Brand.FindById(brandId.Value)?.Name ?? "";
                goodsCommon.GoodsImage = goodsImage;
                goodsCommon.GoodsVideoName = videoUrl;
                goodsCommon.GoodsBody = goodsBody;
                goodsCommon.MobileBody = goods_Mobilebody;
                goodsCommon.GoodsState = goodsState;
                goodsCommon.GoodsShelftime = goodsShelftime;
                goodsCommon.GoodsPrice = goodsPrice;
                goodsCommon.GoodsMarketPrice = goodsMarketPrice;
                goodsCommon.GoodsCostPrice = goodsCostPrice;
                goodsCommon.GoodsDiscount = goodsDiscount;
                goodsCommon.GoodsSerial = goodsSerial;
                //goodsCommon.GoodsStorageAlarm = goodsStorageAlarm ?? 0;
                goodsCommon.GoodsCommend = goodsCommend;
                goodsCommon.GoodsFreight = goodsFreight;
                goodsCommon.GoodsVat = goodsVat;
                goodsCommon.GoodsStcids = goodsStcids;
                goodsCommon.PlateidTop = plateidTop;
                goodsCommon.PlateidBottom = plateidBottom;
                //goodsCommon.IsVirtual = isVirtual.Value;
                //goodsCommon.VirtualType = virtualType.Value;
                //goodsCommon.VirtualIndate = VirtualIndate.Value;
                //goodsCommon.VirtualLimit = virtualLimit.Value;
                goodsCommon.IsGoodsFCode = isGoodsFCode;
                goodsCommon.IsAppoint = isAppoint;
                goodsCommon.AppointSatedate = appointSatedate;
                goodsCommon.Insert();
                #endregion

                #region 商品表
                goods.Id = goodsCommon.Id;
                goods.GoodsCommonId = goodsCommon.Id;
                goods.Name = goodsName;
                goods.AdvWord = advWord;
                goods.StoreId = storeEntity.Id;
                goods.StoreName = storeEntity.Name;
                goods.CId = classId;
                goods.Cid1 = Int64.Parse(classIds[0]);
                goods.Cid2 = Int64.Parse(classIds[1]);
                if (classIds.Length > 2)
                {
                    goods.Cid3 = Int64.Parse(classIds[2]);
                }
                //goods.BrandId = brandId.Value;
                goods.GoodsPrice = goodsPrice;
                goods.GoodsMarketPrice = goodsMarketPrice;
                goods.GoodsSerial = goodsSerial;
                //goods.GoodsStorageAlarm = goodsStorageAlarm.Value;
                goods.MerchantMaterial = merchantMaterial;
                //goods.GoodsWeight = goodsWeight.Value;
                goods.GoodsImage = goodsImage;
                goods.GoodsVideoName = videoUrl;
                goods.GoodsState = goodsState;
                goods.GoodsFreight = goodsFreight;
                goods.GoodsVat = goodsVat;
                goods.GoodsCommend = goodsCommend;
                goods.GoodsStcids = goodsStcids;
                //goods.IsVirtual = isVirtual.Value;
                //goods.VirtualIndate = VirtualIndate.Value;
                //goods.VirtualLimit = virtualLimit.Value;
                goods.IsGoodsFCode = isGoodsFCode;
                goods.IsAppoint = isAppoint;
                goods.MaterialIds = merchantMaterial.SafeString();
                goods.SeoTitle = seotitle;
                goods.SeoKeys = seokeys;
                goods.SeoDescription = seodescription;
                goods.Insert();
                #endregion

                #region 商品分类属性
                ///商品扩展属性表
                List<ClassAttributes> attributeList = ClassAttributes.FindAllByClassIdLan(goodsClass.Id, WorkingLanguage.Id);
                var goodsExAttributes = new GoodsExAttributes();
                foreach (var item in attributeList)
                {
                    string field = GetRequest($"classAttribute_{item.MappingField}").SafeString().Trim();
                    if (field.IsNullOrEmpty()) continue;
                    var property = goodsExAttributes.GetType().GetProperty(item.MappingField ?? "");
                    if (property != null)
                    {
                        var attList = GoodsClassExAttributes.FindAllByClassIdAndFlied(classId, item.MappingField ?? "");
                        if (!attList.Contains(field))
                        {
                            var attModel = new GoodsClassExAttributes();
                            attModel.SetProperty(item.MappingField ?? "", field);
                            attModel.ClassId = classId;
                            attModel.Insert();
                        }
                        goodsExAttributes.SetProperty(item.MappingField ?? "", field);
                    }
                }
                goodsExAttributes.ClassId = classId;
                goodsExAttributes.GoodsId = goods.Id;
                goodsExAttributes.Insert();
                ///分类属性扩展表
                #endregion

                #region 阶梯价格
                if (tieredPrice != null && tieredPrice.Count > 0)
                {
                    var tieredPriceList = new List<GoodsTieredPrice>();

                    // 按MinQuantity排序以确保正确验证
                    tieredPrice = tieredPrice.OrderBy(t => t.MinQuantity).ToList();

                    // 分层定价以实现连续性
                    for (int i = 0; i < tieredPrice.Count; i++)
                    {
                        var tier = tieredPrice[i];

                        // 验证OriginalPrice是否等于goodsPrice
                        if (tier.OriginalPrice != goodsPrice)
                        {
                            return Prompt(new PromptModel { Message = GetResource("阶梯价格的原价必须等于商品价格") });
                        }

                        // 跳过无效条目，但必须验证有效条目的售价
                        if (tier.MinQuantity <= 0)
                        {
                            return Prompt(new PromptModel { Message = GetResource("最小数量必须大于0") });
                        }

                        // 验证售价是否填写
                        if (tier.Price <= 0)
                        {
                            return Prompt(new PromptModel { Message = GetResource("阶梯价格区间必须设置有效的售价") });
                        }

                        // 确保MaxQuantity大于MinQuantity
                        if (tier.MaxQuantity.HasValue && tier.MaxQuantity.Value > 0 && tier.MaxQuantity.Value <= tier.MinQuantity)
                        {
                            return Prompt(new PromptModel { Message = GetResource("阶梯价格区间错误：最大购买数量必须大于最小购买数量") });
                        }

                        // 检查价格等级之间的连续性
                        if (i > 0)
                        {
                            var prevTier = tieredPrice[i - 1];

                            // 如果上一层有最大数量，而此层的最小数量与其不匹配
                            if (prevTier.MaxQuantity.HasValue && prevTier.MaxQuantity.Value > 0 &&
                                prevTier.MaxQuantity.Value + 1 != tier.MinQuantity)
                            {
                                return Prompt(new PromptModel { Message = GetResource("阶梯价格区间错误：价格区间之间不能有断层") });
                            }
                        }

                        var tieredPriceItem = new GoodsTieredPrice
                        {
                            GoodsId = goods.Id,
                            SkuId = goods.Id,//没有SKU属性直接使用商品ID
                            GoodsCommonId = goodsCommon.Id,
                            StoreId = storeEntity.Id,
                            MinQuantity = tier.MinQuantity,
                            MaxQuantity = (i == tieredPrice.Count - 1) ? 0 : tier.MaxQuantity ?? 0, // 0表示无上限
                            OriginalPrice = tier.OriginalPrice,
                            Price = tier.Price,
                            Enabled = true
                        };

                        tieredPriceList.Add(tieredPriceItem);
                    }

                    // 检查重叠范围
                    for (int i = 0; i < tieredPriceList.Count; i++)
                    {
                        for (int j = i + 1; j < tieredPriceList.Count; j++)
                        {
                            var tier1 = tieredPriceList[i];
                            var tier2 = tieredPriceList[j];

                            // 检查范围是否重叠
                            if ((tier1.MaxQuantity == 0 || tier2.MinQuantity < tier1.MaxQuantity) &&
                                (tier2.MaxQuantity == 0 || tier1.MinQuantity <= tier2.MaxQuantity))
                            {
                                return Prompt(new PromptModel { Message = GetResource("阶梯价格区间错误：价格区间不能重叠") });
                            }
                        }
                    }

                    if (tieredPriceList.Count > 0)
                    {
                        tieredPriceList.Insert();
                    }
                }
                #endregion

                #region 商品图片表
                //查询商品图片,添加到商品图片表并设置为默认主图
                var albumPic = AlbumPic.FindByNameAndSId(goodsImage, storeEntity.Id);
                if (albumPic != null)
                {
                    var goodsPic = new GoodsImages();
                    goodsPic.GoodsCommonId = goodsCommon.Id;
                    goodsPic.StoreId = storeEntity.Id;
                    goodsPic.ImageUrl = goodsImage;
                    goodsPic.Sort = 0;
                    goodsPic.IsDefault = 1;
                    goodsPic.Insert();
                }
                #endregion

                #region 商品/图片翻译表
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var localizationSettings = LocalizationSettings.Current;
                var glList = new List<GoodsLan>();
                var picList = new List<GoodsImagesLan>();
                if (localizationSettings.IsEnable)
                {
                    foreach (var item in Languagelist)
                    {
                        var lanModel = new GoodsLan();
                        lanModel.GId = goodsCommon.Id;
                        lanModel.Name = (GetRequest($"[{item.Id}].goodsName")).SafeString().Trim();
                        lanModel.AdvWord = (GetRequest($"[{item.Id}].advWord")).SafeString().Trim();
                        lanModel.GoodsImage = (GetRequest($"[{item.Id}].goodsImage")).SafeString().Trim();
                        lanModel.GoodsVideoName = (GetRequest($"[{item.Id}].videoUrl")).SafeString().Trim();
                        lanModel.Content = (GetRequest($"goods_body_{item.Id}")).SafeString().Trim();
                        lanModel.MobileContent = (GetRequest($"goods_Mobilebody_{item.Id}")).SafeString().Trim();
                        lanModel.SeoTitle = (GetRequest($"[{item.Id}].seotitle")).SafeString().Trim();
                        lanModel.SeoKeys = (GetRequest($"[{item.Id}].seokeys")).SafeString().Trim();
                        lanModel.SeoDescription = (GetRequest($"[{item.Id}].seodescription")).SafeString().Trim();
                        lanModel.LId = item.Id;
                        glList.Add(lanModel);
                        if (!lanModel.GoodsImage.IsNullOrEmpty())//如果翻译表有主图则添加到图片翻译表并设置为主图
                        {
                            var goodsPic = new GoodsImagesLan();
                            goodsPic.GoodsCommonId = goodsCommon.Id;
                            goodsPic.StoreId = storeEntity.Id;
                            goodsPic.ImageUrl = lanModel.GoodsImage;
                            goodsPic.Sort = 0;
                            goodsPic.LId = item.Id;
                            goodsPic.IsDefault = 1;
                            picList.Add(goodsPic);
                        }

                    }
                    glList.Insert();
                    if (picList.Count > 0) picList.Insert();
                }
                #endregion

                tran1.Commit();
            }
            return RedirectToAction("CreateGoodsSku", new { goodsId = goods.Id });
        }
        catch (Exception ex)
        {
            return Prompt(new PromptModel { Message = GetResource("添加商品过程中发生了错误：" + ex.Message) });
            throw;
        }

    }

    [DisplayName("上传商品图片")]
    public IActionResult CreateGoodsPicture(Int64 goodscommId)
    {
        return View(goodscommId);
    }

    /// <summary>
    /// 根据父ID获取分类
    /// </summary>
    /// <param name="pId"></param>
    /// <returns></returns>
    [DisplayName("检索商品分类")]
    public IActionResult SearchGoodsClass(Int64 pId)
    {
        var res = new DResult();
        if (pId==0)
        {
            res.success = false;
            res.msg = "未检索到信息";
        }
        res.data = GoodsClass.FindAllByParentId(pId).Select(x => new
        {
            Id = x.Id.SafeString(),
            x.ParentId,
            x.GoodTypeId,
            Name = GoodsClassLan.FindByGIdAndLId(x.Id, WorkingLanguage.Id)?.RealName ?? x.Name
        });
        res.success = true;
        res.msg = "成功";
        return Json(res);
    }

    /// <summary>
    /// 删除常用分类
    /// </summary>
    /// <param name="stapleId">常用分类ID</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("删除常用分类")]
    public IActionResult DeleteClassStaple(Int64 stapleId)
    {
        var res = new DResult();

        try
        {
            // 查找常用分类
            var staple = GoodsClassStaple.FindById(stapleId);

            // 检查是否存在且属于当前用户
            if (staple == null || staple.UId != ManageProvider.User?.ID)
            {
                res.success = false;
                res.msg = GetResource("找不到常用分类或无权限删除");
                return Json(res);
            }

            // 删除常用分类
            staple.Delete();

            res.success = true;
            res.msg = GetResource("删除成功");
        }
        catch (Exception ex)
        {
            res.success = false;
            res.msg = GetResource("删除失败：") + ex.Message;
        }

        return Json(res);
    }

    /// <summary>
    /// 添加常用分类
    /// </summary>
    /// <param name="classId">分类ID</param>
    /// <param name="className">分类名称</param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("添加常用分类")]
    public IActionResult AddClassStaple(Int64 classId, string className)
    {
        var res = new DResult();

        try
        {
            // 检查分类是否存在
            var goodsClass = GoodsClass.FindById(classId);
            if (goodsClass == null)
            {
                res.success = false;
                res.msg = GetResource("分类不存在");
                return Json(res);
            }

            // 检查是否已经添加过该分类
            var existingStaple = GoodsClassStaple.FindAllByUId(ManageProvider.User?.ID??0)
                .FirstOrDefault(s => s.Cid2 == classId);

            if (existingStaple != null)
            {
                // 更新使用次数
                existingStaple.StapleCounter += 1;
                existingStaple.Update();

                res.success = true;
                res.msg = GetResource("已更新常用分类使用次数");
                res.data = new { id = existingStaple.Id, name = existingStaple.StapleName };
                return Json(res);
            }

            // 创建新的常用分类
            var staple = new GoodsClassStaple();
            staple.UId = ManageProvider.User?.ID??0;
            staple.StapleName = className;
            staple.StapleCounter = 1;

            // 获取分类路径
            var classPath = goodsClass.ParentIdList.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (classPath.Length > 0)
            {
                staple.Cid1 = Int64.Parse(classPath[0]);
            }
            staple.Cid2 = classId;

            // 保存常用分类
            staple.Insert();

            res.success = true;
            res.msg = GetResource("添加成功");
            res.data = new { id = staple.Id, name = staple.StapleName };
        }
        catch (Exception ex)
        {
            res.success = false;
            res.msg = GetResource("添加失败：") + ex.Message;
        }

        return Json(res);
    }
    [DisplayName("检索省份")]
    public IActionResult GetProvince(string cId)
    {
        var result = new DResult();
        if (cId.IsNullOrEmpty())
        {
            result.msg = "找不到省份数据！";
            return Json(result);
        }
        var list = Regions.FindAllByCIdAndLevel(cId, 0);
        if (list.Count==0)
        {
            result.msg = "找不到省份数据！";
            return Json(result);
        }
        result.success = true;
        result.msg = "获取到省份数据！";
        result.data = list.Select(x => new
        {
            x.Id,
            x.AreaCode,
            x.Name
        });
        return Json(result);
    }
    [DisplayName("检索子集城市")]
    public IActionResult GetCiyt(string pCode)
    {
        var result = new DResult();
        if (pCode == null)
        {
            result.msg = "找不到城市数据！";
            return Json(result);
        }
        var list = Regions.FindAllByParentCode(pCode);
        if (list.Count == 0)
        {
            result.msg = "找不到城市数据！";
            return Json(result);
        }
        result.success = true;
        result.msg = "获取到城市数据！";
        result.data = list.Select(x => new
        {
            x.Id,
            x.AreaCode,
            x.Name
        });
        return Json(result);
    }
    /// <summary>
    /// 图片上传
    /// </summary>
    /// <returns></returns>
    [DisplayName("图片上传")]
    [HttpPost]
    public IActionResult UploadImg()
    {
        var filea = Request.Form.Files;
        var list = filea.Count();

        var img = filea.FirstOrDefault();
        var bytes = img!.OpenReadStream().ReadBytes(img.Length);
        if (!bytes.IsImageFile())
        {
            return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！"), IsOk = false });
        }
        var store = Store.FindByUId(ManageProvider.User?.ID??0);
        var albumCategory = AlbumCategory.FindDefaultByStoreId(store?.Id ?? 0);
        if (albumCategory==null)//创建默认相册
        {
            using (var tran = AlbumCategory.Meta.CreateTrans())
            {
                albumCategory = new AlbumCategory();
                albumCategory.Name = "默认相册";
                albumCategory.StoreId = store?.Id ?? 0;
                albumCategory.IsDefault = true;
                albumCategory.Sort = 255;
                albumCategory.Insert();
                tran.Commit();
            }
        }

        var filename = Randoms.MakeFileRndName() + Path.GetExtension(img.FileName); ;
        var filepath = $"AlbumPic/{store?.Id ?? 0}/{albumCategory.Id}/{filename}";//AlbumPic/店铺ID/相册ID/文件名
        var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
        filepath = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
        saveFileName.EnsureDirectory();
        var Models = new AlbumPic();
        using (var tran1 = AlbumPic.Meta.CreateTrans())
        {
            var image = Image.Load(bytes);

            var Spec = image.Width + "X" + image.Height;

            Models.Name = filename;
            Models.AId = albumCategory.Id;
            Models.StoreId = store?.Id ?? 0;
            Models.Cover = filepath.Replace("\\", "/");
            Models.Spec = Spec;
            Models.Size = Convert.ToInt32(img.Length / 1024);
            Models.Insert();
            tran1.Commit();
        }
        img.SaveAs(saveFileName);

        AlbumPic.Meta.Cache.Clear("");//清除缓存
        return Json(new { success = true, file_id = Models.Id, state = true, origin_file_name = img.FileName, file_name = filename, file_path = filepath });
    }
    [HttpPost]
    [DisplayName("上传图片")]
    public IActionResult UploadPicture(IFormFile aPic)
    {
        if (aPic==null) return Json(new DResult() { msg = "未找到图片对象，请重新上传", success = false });
        Int32 picSize = Convert.ToInt32(aPic.Length / 1024);//kb
        if (picSize>1000) return Json(new DResult() { msg = "图片大小超过1M，请重新上传", success = false });
        try
        {
            var store = Store.FindByUId(ManageProvider.User?.ID??0);

            var albumCategory = AlbumCategory.FindDefaultByStoreId(store?.Id??0);
            if (albumCategory==null)//创建默认相册
            {
                using (var tran = AlbumCategory.Meta.CreateTrans())
                {
                    albumCategory = new AlbumCategory();
                    albumCategory.Name = "默认相册";
                    albumCategory.StoreId = store?.Id ?? 0;
                    albumCategory.IsDefault = true;
                    albumCategory.Sort = 255;
                    albumCategory.Insert();
                    tran.Commit();
                }
            }
            var result = new DResult();
            using (var tran1 = AlbumPic.Meta.CreateTrans())
            {

                var alblumpic = new AlbumPic();
                if (aPic != null)
                {
                    var bytes = aPic.OpenReadStream().ReadBytes(aPic.Length);
                    if (!bytes.IsImageFile())
                    {
                        return Prompt(new PromptModel { Message = GetResource("非法操作，请上传图片！") });
                    }
                    var filename = Randoms.MakeFileRndName()+Path.GetExtension(aPic.FileName); ;
                    var filepath = $"AlbumPic/{store?.Id ?? 0}/{albumCategory.Id}/{filename}";//AlbumPic/店铺ID/相册ID/文件名
                    var saveFileName = DHSetting.Current.UploadPath.GetFullPath().CombinePath(filepath);
                    filepath = $"/{DHSetting.Current.UploadPath}/" + filepath.Replace("\\", "/");
                    saveFileName.EnsureDirectory();
                    alblumpic.Name = filename;
                    alblumpic.AId = albumCategory.Id;
                    alblumpic.Cover = filepath;
                    alblumpic.StoreId = store?.Id ?? 0;
                    alblumpic.Size = picSize;
                    alblumpic.Insert();
                    aPic.SaveAs(saveFileName);
                    result.msg = filepath;
                    result.data = alblumpic;
                }
                tran1.Commit();
            }
            result.success = true;
            return Json(result);
        }
        catch (Exception ex)
        {
            return Prompt(new PromptModel { Message = GetResource("上传图片过程中发生错误："+ex.Message) });
        }
    }
    [DisplayName("检索相册图片")]
    public IActionResult GetAlnumPic(Int32 aId,Int32 page = 1,Int32 limit = 12)
    {
        if (aId.IsNull()) return Json(new DResult() { success = false , msg = "相册ID为空"});
        var result = new DResult();
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = "Id",
            Desc = true
        };
        var albumPic = AlbumPic.Search(aId, DateTime.MinValue, DateTime.MaxValue, "", pages);
        result.data = albumPic.Select(x => new
        {
            x.Id,
            x.Name,
            x.Cover
        });
        result.msg = $"检索到：{albumPic.Count}条记录";
        result.extdata = pages;
        result.success = true;
        return Json(result);
    }
    /// <summary>
    /// 插入主图列表
    /// </summary>
    /// <param name="id"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [DisplayName("相册图片列表")]
    public IActionResult Piclist(int id, int page = 1)
    {
        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true
        };
        ViewBag.list = AlbumPic.Searchs(id, "", pages).Select(x => new AlbumPic { Id = x.Id,Name = x.Name, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover) });
        ViewBag.ListXC = AlbumCategory.FindAllByStoreId(Store.FindByUId(ManageProvider.User?.ID??0)?.Id??0);
        ViewBag.Page = pages;
        return PartialView();
    }

    [DisplayName("插入详情相册图片列表")]
    public IActionResult PiclistConten(int id, string type, int page = 1)
    {

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true
        };
        var list = AlbumPic.Searchs(id, "", pages).Select(x => new AlbumPic { Id = x.Id, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover) });
        ViewBag.list = list;
        ViewBag.ListXC = AlbumCategory.FindAllWithCache();
        ViewBag.type = type;
        ViewBag.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("PiclistConten"), new Dictionary<String, String> { { "id", id.ToString() }, { "type", type } });
        return PartialView();
    }

    [DisplayName("插入详情相册图片列表")]
    public IActionResult PiclistContens(int id, string type, int page = 1)
    {

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true
        };
        ViewBag.list = AlbumPic.Searchs(id, "", pages).Select(x => new AlbumPic { Id = x.Id, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover) });
        ViewBag.ListXC = AlbumCategory.FindAllWithCache();
        ViewBag.type = type;
        ViewBag.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("PiclistContens"), new Dictionary<String, String> { { "id", id.ToString() }, { "type", type } });
        return View();
    }

    /// <summary>
    /// 插入多图列表
    /// </summary>
    /// <param name="id"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("相册图片列表")]
    public IActionResult Piclistmultiple(int id, int page = 1)
    {

        var pages = new PageParameter()
        {
            PageIndex = page,
            PageSize = 12,
            RetrieveTotalCount = true
        };
        ViewBag.list = AlbumPic.Searchs(id, "", pages).Select(x => new AlbumPic { Id = x.Id, Cover = UrlHelper.Combine(Pek.Helpers.DHWeb.GetSiteUrl(), x.Cover), Spec = x.Cover });
        ViewBag.ListXC = AlbumCategory.FindAllWithCache();

        ViewBag.Str = PageHelper.CreatePage(page, pages.TotalCount, pages.PageCount, Url.Action("Piclistmultiple"), new Dictionary<String, String> { { "id", id.ToString() } });
        return View();
    }


    [HttpPost]
    [DisplayName("添加图片到图片表")]
    public IActionResult CreateGoodsIamges(IFormCollection form)
    {
        try
        {
            Int64 commonId = form["commonid"].ToLong();
            if (commonId.IsNull())return Json(new { success = false, message = "商品公共ID为空" });
            Int64 storeId = Store.FindByUId(ManageProvider.User?.ID??0)?.Id??0;
            var goodsCommon = GoodsCommon.FindById(commonId);
            if (goodsCommon==null) return Json(new { success = false, message = "商品信息不存在" });

            var images = new List<GoodsImages>();
            using (var tran = GoodsImages.Meta.CreateTrans())
            {
                // 遍历处理5张图片
                for (int i = 0; i < 5; i++)
                {
                    var fileId = form[$"img[0][{i}][Id]"];
                    var name = form[$"img[0][{i}][name]"].ToString();
                    if (name.IsNullOrEmpty()) continue;
                    var isdefault = form[$"img[0][{i}][default]"].ToInt();
                    var sort = form[$"img[0][{i}][sort]"].ToInt();       //判断图片是否已经存在
                    var fileModel = GoodsImages.FindById(fileId.ToInt());
                    var gImg = new GoodsImages();
                    if (fileModel != null)
                    {
                        gImg = fileModel;
                    }

                    //创建信息已经添加过了，为了保持一致性
                    gImg.GoodsCommonId = commonId;
                    gImg.ImageUrl = name;
                    gImg.IsDefault = isdefault;
                    gImg.StoreId = storeId;
                    gImg.Sort = sort;
                    images.Add(gImg);
                    if (isdefault == 1 && name != goodsCommon.GoodsImage)//如果商品图片主图发生变化则修改
                    {
                        XTrace.WriteLine("我到这里了");
                        goodsCommon.GoodsImage = name;
                        goodsCommon.Update();
                        var goods = Goods.FindByCIdAndSId(commonId, storeId);
                        if (goods != null)
                        {
                            goods.GoodsImage = name;
                        goods.Update();
                        }
                        
                    }
                }
                if (images.Count > 0)
                {
                    images.Save();
                }

                //图片翻译表
                var localizationSettings = LocalizationSettings.Current;
                var Languagelist = Language.FindByStatus().OrderBy(e => e.DisplayOrder); //获取全部有效语言
                var imageslan = new List<GoodsImagesLan>();

                if (localizationSettings.IsEnable)
                {
                    foreach (var item in Languagelist)
                    {
                        // 遍历处理5张图片
                        for (int i = 0; i < 5; i++)
                        {
                            var fileId = form[$"[{item.Id}].img[0][{i}][Id]"];
                            var name = form[$"[{item.Id}].img[0][{i}][name]"].ToString();
                            if (name.IsNullOrEmpty()) continue;
                            var isdefault = form[$"[{item.Id}].img[0][{i}][default]"].ToInt();
                            var sort = form[$"[{item.Id}].img[0][{i}][sort]"].ToInt();
                            //判断图片是否已经存在
                            var fileModel = GoodsImagesLan.FindById(fileId.ToInt());
                            var gImg = new GoodsImagesLan();
                            if (fileModel != null)
                            {
                                gImg = fileModel;
                            }
                            //创建信息已经添加过了，为了保持一致性
                            gImg.GoodsCommonId = commonId;
                            gImg.ImageUrl = name;
                            gImg.IsDefault = isdefault;
                            gImg.StoreId = storeId;
                            gImg.Sort = sort;
                            gImg.LId = item.Id;
                            imageslan.Add(gImg);
                            if (isdefault == 1)//如果商品图片主图发生变化则修改
                            {
                                var goods = Goods.FindByCIdAndSId(commonId, storeId);
                                var goodslan = GoodsLan.FindByGIdAndLId(commonId, item.Id);
                                if (goodslan != null && name != goodslan.GoodsImage)
                                {
                                    goodslan.GoodsImage = name;
                                    goodslan.Update();
                                }

                            }
                        }
                        if (imageslan.Count > 0)
                        {
                            imageslan.Save();
                        }
                    }
                }
                tran.Commit();
            }
            dynamic viewModel = new ExpandoObject();
            viewModel.goodscommon = goodsCommon;
            return RedirectToAction("Success",new { goodsCommonId = goodsCommon?.Id } );
        }
        catch (Exception ex)
        {
            return Prompt(new PromptModel { Message = GetResource("上传图片过程中发生错误：" + ex.Message) });
        }
    }
    [DisplayName("发布成功")]
    public IActionResult Success(Int64 goodsCommonId)
    {
        return View(goodsCommonId);
    }
    /// <summary>
    /// 添加常用分类记录
    /// </summary>
    /// <param name="cId"></param>
    /// <returns></returns>
    public void CreataGoodsClassStaple(Int64 cId)
    {
        var goodsClass = GoodsClass.FindById(cId);
        if (goodsClass == null) return; // 如果分类不存在则不进行任何操作
        var cIds = goodsClass.ParentIdList.Split(",",StringSplitOptions.RemoveEmptyEntries);
        var classStaple = GoodsClassStaple.GetModelByUIdAndCId(ManageProvider.User?.ID??0,cId);
        if (classStaple==null)
        {
            classStaple = new GoodsClassStaple();
            string classNames = "";
            if (cIds.Length > 0)
            {
                classStaple.Cid1 = Convert.ToInt64(cIds[0]);
                classNames += GoodsClass.FindById(classStaple.Cid1) + ">";
            }
            if (cIds.Length > 1)
            {
                classStaple.Cid2 = Convert.ToInt64(cIds[1]);
                classNames += GoodsClass.FindById(classStaple.Cid2);
            }
            classStaple.StapleName = classNames;
            classStaple.UId = ManageProvider.User?.ID??0;
            classStaple.StapleCounter = 1;
            classStaple.Insert();
        }
        else
        {
            classStaple.StapleCounter++;
            classStaple.Update();
            GoodsClassStaple.Meta.Cache.Clear("", true);
        }
    }
    public IActionResult CreateGoodsSku(long goodsId)
    {
        if (goodsId.IsNull())
        {
            return Prompt(new PromptModel { Message = GetResource("商品ID无效") });
        }
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品不存在") });
        }
        dynamic viewModel = new ExpandoObject();
        viewModel.goods = goods;
        var specValueList = GoodsSpecValue.FindAllByGoodsId(goodsId);
        viewModel.specValueList = specValueList;
        viewModel.specList = GoodsSpecValue.FindAllByParentIds("0");
        return View(viewModel);
    }
    [HttpPost]
    [DisplayName("添加商品SKU")]
    public IActionResult AddGoodsSku(long goodsId)
    {
        if (goodsId.IsNull())
        {
            return Prompt(new PromptModel { Message = GetResource("商品ID无效") });
        }
        var goods = Goods.FindById(goodsId);
        if (goods == null)
        {
            return Prompt(new PromptModel { Message = GetResource("商品不存在") });
        }
        #region 商品SKU
        if (!GetRequest($"skucount").IsNullOrEmpty())
        {
            using (var tran = GoodsSKUDetail.Meta.CreateTrans())
            {
                List<GoodsSKUDetail> skuList = new List<GoodsSKUDetail>();
                var skucount = Convert.ToInt32((GetRequest($"skucount")).SafeString().Trim());
                for (int i = 0; i < skucount; i++)
                {
                    int specCount = Convert.ToInt32((GetRequest($"specValueCount")).SafeString().Trim());
                    List<GoodsSpecDto> specDtos = new();
                    for (int j = 0; j < specCount; j++)
                    {
                        var specId = Convert.ToInt32((GetRequest($"spec[{i}][sp_value][{j}]")).SafeString().Trim());
                        var spec = GoodsSpecValue.FindById(specId);
                        if (spec == null) continue;
                        specDtos.Add(new GoodsSpecDto()
                        {
                            Id = spec.ParentId,
                            Name = spec.ParentName ?? "",
                            VId = spec.Id,
                            Value = spec.Name ?? "",
                        });
                    }
                    var sku = new GoodsSKUDetail();
                    sku.SpecValue = specDtos.ToJson();
                    sku.GoodsId = goods.Id;
                    long materialId = Convert.ToInt64((GetRequest($"spec[{i}][material]")).SafeString().Trim());
                    var material = MerchantMaterial.FindById(materialId);
                    if (material == null)
                    {
                        return Prompt(new PromptModel { Message = GetResource("物料不存在") });
                    }
                    sku.MaterialId = materialId;
                    sku.GoodsMarketPrice = Convert.ToDecimal((GetRequest($"spec[{i}][marketprice]")).SafeString().Trim());
                    sku.GoodsPrice = Convert.ToDecimal((GetRequest($"spec[{i}][price]")).SafeString().Trim());
                    sku.Insert();
                    skuList.Add(sku);
                    #region 阶梯价格
                    var tiereddata = GetRequest($"tiered[{i}][data]").SafeString();
                    if (!tiereddata.IsNullOrEmpty())
                    {
                        var tieredlist = JsonConvert.DeserializeObject<List<TieredPriceDto>>(tiereddata);
                        if (tieredlist != null && tieredlist?.Count > 0)
                        {
                            foreach (var item in tieredlist)
                            {
                                var goodstieredPrice = new GoodsTieredPrice()
                                {
                                    GoodsId = goods.Id,
                                    SkuId = sku.Id,
                                    StoreId = goods.StoreId,
                                    MinQuantity = item.MinQuantity,
                                    MaxQuantity = item.MaxQuantity,
                                    OriginalPrice = sku.GoodsPrice,
                                    Price = item.Price,
                                    Enabled = true
                                };
                                goodstieredPrice.Insert();
                            }
                        }

                    }
                    #endregion
                }
                if (skuList.Count > 0)
                {
                    string materialIds = skuList.Select(e => e.MaterialId).Join(",");
                    //skuList.Insert();
                    goods.MerchantMaterial = skuList.FirstOrDefault().MaterialId;
                    goods.MaterialIds = materialIds;
                    goods.Update();
                }
                tran.Commit();
            }
        }
        #endregion
        return RedirectToAction("CreateGoodsPicture", new { goodscommId = goods.GoodsCommonId });
    }
}

public class TieredPriceInput
{
    public int MinQuantity { get; set; }
    public int? MaxQuantity { get; set; }
    public decimal OriginalPrice { get; set; }
    public decimal Price { get; set; }
}
