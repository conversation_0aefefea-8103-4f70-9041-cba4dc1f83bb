@* 服务投诉 *@
@{
  PekHtml.AppendCssFileParts("~/public/css/pageCss/service.css");
}
<div class="service">
  <ul class="top">
    <li>
      <img src="~/public/images/icons/dianhua.png" alt="">
      <div class="wen">
        <span>在线客服热线：</span>
        <span>0755-23152658</span>
      </div>
    </li>
    <li>
      <img src="~/public/images/icons/youxiang.png" alt="">
      <div class="wen">
        <span>企业邮箱：</span>
        <span><EMAIL></span>
      </div>

    </li>
    <li>
      <img src="~/public/images/icons/shijian.png" alt="">
      <div class="wen">
        <span>服务时间：</span>
        <span class="day">工作日 9:15~20:30</span>
        <span class="day">节假日 9:15~18:40</span>
      </div>
    </li>
  </ul>
  <main>
    <h3>服务与投诉</h3>
    <form class="layui-form">
      <div class="layui-row ">
        <div class="layui-col-md5 layui-col-md-offset1">
          <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-block">
              <input type="text" name="username" lay-verify="required" placeholder="请输入姓名" autocomplete="off"
                class="layui-input" value="1">
            </div>
          </div>
        </div>
        <div class="layui-col-md5">
          <div class="layui-form-item">
            <label class="layui-form-label">电话</label>
            <div class="layui-input-block">
              <input type="tel" name="phone" lay-verify="required|phone" autocomplete="off" lay-reqtext="请填写手机号"
                lay-affix="clear" placeholder="请输入手机号" value="13800000000" class="layui-input demo-phone">
            </div>
          </div>
        </div>
      </div>
      <div class="layui-row">
        <div class="layui-col-md5 layui-col-md-offset1">
          <div class="layui-form-item">
            <label class="layui-form-label">邮箱</label>
            <div class="layui-input-block">
              <input type="text" name="email" lay-verify="required|email" placeholder="请输入邮箱" autocomplete="off"
                class="layui-input" value="<EMAIL>">
            </div>
          </div>
        </div>
        <div class="layui-col-md5">
          <div class="layui-form-item">
            <label class="layui-form-label">公司名称</label>
            <div class="layui-input-block">
              <input type="text" name="companyName" lay-verify="required" placeholder="请输入公司名称" autocomplete="off"
                class="layui-input" value="3">
            </div>
          </div>
        </div>
      </div>
      <div class="layui-row">
        <div class="layui-col-md5 layui-col-md-offset1">
          <div class="layui-form-item">
            <label class="layui-form-label">主题</label>
            <div class="layui-input-block">
              <input type="text" name="theme" lay-verify="required" placeholder="请输入" autocomplete="off"
                class="layui-input" value="4">
            </div>
          </div>
        </div>
        <div class="layui-col-md5">
          <div class="layui-form-item">
            <label class="layui-form-label">所在区域</label>
            <div class="layui-input-block">
              <select name="quiz1" lay-verify="required" lay-filter="country">
                <option value="中国">中国</option>
                <option value="美国">美国</option>
                <option value="英国">英国</option>
              </select>
            </div>
            @* <div class="layui-input-inline">
              <select name="quiz2" lay-verify="required">
                <option value="" selected>请选择省</option>
                <option value="浙江">浙江省</option>
                <option value="你的工号">江西省</option>
                <option value="你最喜欢的老师">福建省</option>
              </select>
            </div>
            <div class="layui-input-inline">
              <select name="quiz3" lay-verify="required">
                <option value="">请选择市</option>
                <option value="杭州">杭州</option>
                <option value="宁波" disabled>宁波</option>
                <option value="温州">温州</option>
                <option value="温州">台州</option>
                <option value="温州">绍兴</option>
              </select>
            </div> *@


          </div>
        </div>
      </div>
      <div class="layui-row">
        <div class="layui-col-md10 layui-col-md-offset1">
          <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">建议与反馈</label>
            <div class="layui-input-block">
              <textarea id="myTextarea" placeholder="请输入内容" lay-verify="required" class="layui-textarea"></textarea>
            </div>
          </div>
        </div>
      </div>
      <div class="layui-row">
        <div class="layui-col-md3 layui-col-md-offset5">
          <div class="layui-form-item">
            <button class="layui-btn layui-bg-blue" lay-submit lay-filter="demo1">确认</button>
          </div>
        </div>
      </div>
    </form>
  </main>

</div>
<div class="bug"></div>
<script>
  document.getElementById("myTextarea").value = "新的文本内容";
  document.addEventListener('DOMContentLoaded', function () {
    layui.use(['form', 'laydate', 'util'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var laydate = layui.laydate;
      var util = layui.util;


      // 提交事件
      form.on('submit(demo1)', function (data) {
        var field = data.field; // 获取表单字段值
        // 显示填写结果
        console.log('field :>> ', field);

        // 此处可执行 Ajax 等操作
        // …
        
        return false; // 阻止默认 form 跳转
      });

      




    });
  });


</script>