@using B2B2CShop.Dto
@using B2B2CShop.Entity
@{
    // 页面样式
    PekHtml.AppendPageCssClassParts("html-main-page");
    // Css
    PekHtml.AppendCssFileParts("~/public/css/pageCss/connection.css");

    PekHtml.AppendScriptParts(ResourceLocation.Head, "~/lib/select2/js/select2.min.js");
    var site = SiteInfo.GetDefaultSeo();
}
<script asp-location="Head">
    var NoResultsFound = '@T("No results found")';
</script>
<style>
    /*     .selectBox{
    position:absolute;
    width:300px;
    height:50px;
    z-index:9999999;
    } */
    .layui-layer-title {
    font-size: 1vw;
    letter-spacing: 1px;

    }

    .editAddrBox {
    width: 30vw;
    max-width: 500px;
    min-width: 400px;
    max-height: 80vh;
    overflow: auto;
    padding: 1vw 1vw .2vw 1vw;
    }

    .distributionBox {
    width: 100%;
    padding: 7px;
    justify-content: left;
    background-color: #F2F2F2;
    }

    .layui-form-label {
    color: var(--text-color2);
    }

    .layui-form-item {
    width: 100%;
    display: flex;
    position: relative;
    /* border: 1px solid var(--line); */
    }

    .layui-inline {
    width: 100%;
    margin-right: 0px !important;
    display: flex;
    }

    .layui-inline > div:last-child {
    margin-right: 0px !important;
    padding-right: 0px !important;
    }

    .layui-input-inline {
    flex: 1;
    /* background-color: red; */
    }

    .failText {
    position: absolute;
    left: 120px;
    top: 92%;
    font-size: 12px;
    color: var(--red);
    display: none;
    }

    .dialog_editAddr > .layui-layer-btn {
    padding: 0px 0px;
    margin: 0px;
    margin-bottom: 20px;
    width: 100%;
    display: flex;
    /* border: 1px solid ; */
    font-size: 17px;
    height: 50px;
    }
    /* 取消按钮 */
    .dialog_editAddr > .layui-layer-btn > a:nth-child(1) {
    margin-left: 130px;
    width: 20%;
    min-width: 120px;
    background-color: white;
    color: var(--text-color);
    border: 1px solid #C4C6CF;
    text-align: center;
    height: 40px;
    line-height: 40px;
    border-radius: 7px;
    }
    /* 取消按钮 */
    .dialog_editAddr > .layui-layer-btn > a:nth-child(2) {
    margin-left: 40px;
    width: 20%;
    min-width: 120px;
    background-color: var(--blue-deep);
    color: white;
    text-align: center;
    height: 40px;
    line-height: 40px;
    border-radius: 7px;
    margin-right: auto;
    }

    .layui-anim-upbit {
    z-index: 9999;
    }

    .cancelBtn {
    margin-left: 110px;
    width: 25%;
    min-width: 104px;
    background-color: white;
    color: var(--text-color);
    border: 1px solid #C4C6CF;
    text-align: center;
    height: 40px;
    line-height: 40px;
    border-radius: 7px;
    }

    .saveBtn {
    margin-left: auto !important;
    margin-right: auto;
    width: 25%;
    min-width: 104px;
    background-color: var(--blue-deep);
    color: white;
    text-align: center;
    height: 40px;
    line-height: 40px;
    border-radius: 7px;
    }

    .address-detail {
        white-space: normal; /* 允许文本换行 */
        word-wrap: break-word; /* 如果单词太长，允许单词换行 */
        word-break: break-word; /* 强制长单词换行 */
        overflow: hidden; /* 隐藏超出容器的内容（可选） */
        display: block; /* 确保是块级元素 */
        line-height: 1.5; /* 设置行高，增加可读性 */
        margin-left: 23px;
    }
</style>
<div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
@*         <a href="/">@T("首页")</a>
        <a>></a> *@
        <a href="@Url.Action("Index", "Account", new { area = "Member" })">@T("账号中心")</a>
        <a>></a>
        <a class="textSelect" href="@Url.Action("Index", "Contact")">@T("联系方式")</a>
    </div>
    <!-- 用户中心 -->
    <div class="userInfoBox">
        <aside>
            <div>@T("账户中心")</div>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Account", new { area = "Member" })">
                <div class="iconfont icon-weidenglu"></div>
                <div>@T("账号信息")</div>
            </a>
            <a href="@Url.Action("Index", "Orders", new { area = "Member" })">
                <div class="iconfont icon-a-description2x"></div>
                <div>@T("订单")</div>
            </a>
            <a href="@Url.Action("Index", "Refund", new { area = "Member" })">
                <div class="iconfont icon-wuliu"></div>
                <div>@T("退货和退款")</div>
            </a>
            <a href="@Url.Action("Index", "Wish", new { area = "Member" })">
                <div class="iconfont icon-heart"></div>
                <div>@T("心愿清单")</div>
            </a>
            <a href="@Url.Action("Index", "GoodsBrowse", new { area = "Member" })">
                <div class="iconfont icon-lishi"></div>
                <div>@T("浏览历史")</div>
            </a>
            <a class="_line"></a>
@*             <a href="@Url.Action("Index", "Message", new { area = "Member" })">
                <div class="iconfont icon-xiaoxitongzhi"></div>
                <div>@T("信息中心")</div>
            </a> *@
            <a href="@Url.Action("Index", "Evaluate", new { area = "Member" })">
                <div class="iconfont icon-edit"></div>
                <div>@T("评价")</div>
            </a>
            <a href="@Url.Action("Index", "Invoice", new { area = "Member" })">
                <div class="iconfont icon-wuliuxinxi"></div>
                <div>@T("发票")</div>
            </a>
            <a class="_line"></a>
            <a href="@Url.Action("Index", "Setting", new { area = "Member" })">
                <div class="iconfont icon-shezhi2"></div>
                <div>@T("账户设置")</div>
            </a>
            <a href="@Url.Action("Index", "Contact", new { area = "Member" })" class="bgSelect">
                <div class="iconfont icon-dianhua"></div>
                <div>@T("联系方式")</div>
            </a>
@*             <a href="@Url.Action("Index", "PaymentMethod", new { area = "Member" })">
                <div class="iconfont icon-creditcard"></div>
                <div>@T("支付方式")</div>
            </a> *@
        </aside>
        <div class="content">
            <div class="titleBox1">
                <div data-select="false" onclick="titleClick(this,0)" class="titleSelect" style="margin-left: 1vw;">@T("配送地址")</div>
                <div data-select="false" onclick="titleClick(this,1)" style="margin-left: 10%;">@T("发票地址")</div>
                <div style="margin-left: 10%;">
                    <button class="button button_blue" id="btnaddAddress" onclick="openAddressDialog(0)">
                        <i class="iconfont icon-add"></i> @T("添加地址")
                    </button>
                </div>
                <script>
                var addressType = 0;
                function titleClick(dom,index) {
                    addressType = index;
                    $(dom).siblings().removeClass('titleSelect');
                    $(dom).addClass('titleSelect');
                    if (index == 0) {
                        $('#addrBox1').nextUntil('.bug').hide();
                        if ($('#addrBox1')[0].style.display === 'none') 
                        {
                            $('.titleBox1').nextUntil('#addrBox2').show();
                        }
                    }
                    else
                    {
                        $('.titleBox1').nextUntil('#addrBox2').hide();
                        if ($('#addrBox2')[0].style.display === 'none') 
                        {
                            $('#addrBox1').nextUntil('.bug').show();
                        }
                    }
                }
                </script>
            </div>

            <div class="addrBox" id="addrBox1">
                @foreach (BuyerAddress item in Model.AddressList1)
                {
                    if (item.IsDefaultDelivery)
                    {
                        <div class="card defaultBox" onclick="selectDiv(this,'defaultBox')" data-select="true">
                            <div> <i class="iconfont icon-weidenglu"></i> @item.RealName @item.Phone</div>
                            <div>
                                <i class="iconfont icon-location"></i>
                                <span>@<EMAIL></span>
                            </div>
                            <div style="padding-top: 1vw;color: var(--text-color2);">
                                <span class="iconfont icon-location" style="font-size:14px">@T("默认地址")</span>
                                &nbsp;&nbsp;<span class="hoverBlue" onclick="openEditressDialog(@item.Id,0)">@T("修改")</span>&nbsp;&nbsp;
                                <span class="hoverRed" onclick="openDialog2('@T("警告")','@T("您确定要删除该地址？")',@item.Id)">@T("删除")</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="card" onclick="selectDiv(this,'defaultBox')" data-select="false">
                            <div> <i class="iconfont icon-weidenglu"></i> @item.RealName @item.Phone</div>
                            <div>
                                <i class="iconfont icon-location"></i>
                                <span>@<EMAIL></span>
                            </div>
                            <div style="padding-top: 1vw;color: var(--text-color2);">
                                &nbsp;&nbsp;<span class="hoverBlue" onclick="openEditressDialog(@item.Id,0)">@T("修改")</span>&nbsp;&nbsp;
                                <span class="hoverRed" onclick="openDialog2('@T("警告")','@T("您确定要删除该地址？")',@item.Id)">@T("删除")</span>
                            </div>
                        </div>
                    }
                }

            </div>

            <div class="addrBox" id="addrBox2" style="display: none;">
                @foreach (BuyerAddress item in Model.AddressList2)
                {
                    if (item.IsDefaultInvoice)
                    {
                        <div class="card defaultBox" onclick="selectDiv(this,'defaultBox')" data-select="true">
                            <div> <i class="iconfont icon-weidenglu"></i> @item.RealName @item.Phone</div>
                            <div>
                                <i class="iconfont icon-location"></i>
                                <span>@<EMAIL></span>
                            </div>
                            <div style="padding-top: 1vw;color: var(--text-color2);">
                                <span class="iconfont icon-location" style="font-size:14px">@T("默认地址")</span>
                                &nbsp;&nbsp;<span class="hoverBlue" onclick="openEditressDialog(@item.Id,1)">@T("修改")</span>&nbsp;&nbsp;
                                <span class="hoverRed" onclick="openDialog2('@T("警告")','@T("您确定要删除该地址？")',@item.Id)">@T("删除")</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="card" onclick="selectDiv(this,'defaultBox')" data-select="false">
                            <div> <i class="iconfont icon-weidenglu"></i> @item.RealName @item.Phone</div>
                            <div>
                                <i class="iconfont icon-location"></i>
                                <span>@<EMAIL></span>
                            </div>
                            <div style="padding-top: 1vw;color: var(--text-color2);">
                                &nbsp;&nbsp;<span class="hoverBlue" onclick="openEditressDialog(@item.Id,1)">@T("修改")</span>&nbsp;&nbsp;
                                <span class="hoverRed" onclick="openDialog2('@T("警告")','@T("您确定要删除该地址？")',@item.Id)">@T("删除")</span>
                            </div>
                        </div>
                    }
                }
                @* 
                <div class="card defaultBox" onclick="selectDiv(this,'defaultBox')" data-select="true">
                    <div> <i class="iconfont icon-weidenglu"></i> 发票地址，+86 1865492459</div>
                    <div> <i class="iconfont icon-location"></i> 广东省深圳市龙华区民治街道星河WORLDE栋大厦17层</div>
                    <div style="padding-top: 1vw;color: var(--text-color2);">
                        <span class="iconfont icon-location">默认地址</span>
                        &nbsp;&nbsp;<span class="hoverBlue" onclick="editAddr(this)">修改</span>&nbsp;&nbsp;
                        <span class="hoverRed" onclick="openDialog('警告','您确定要删除该地址？','callback')">删除</span>
                    </div>
                </div> *@
            </div>
        </div>
    </div>

</div>
<script asp-location="Footer">

    function openDialog2(title = '温馨提示',content = '您确定要执行此操作吗?',id) {
    layui.layer.open({
        title: title,
        content: content,
        area: '20vw',
        btn: ['@T("确认")', '@T("取消")'],
        yes: function(index, layero){
            layer.close(index);
                    $.post('@Url.Action("Delete")',{Id:id},function(res)
                    {
                          layui.layer.msg(res.msg);
                          if(res.success){
                                location.reload();
                          }
                    })
        },
        btn2: function(index, layero){
            layer.close(index);
        },
    })
}
  // function bomConfirm(closeIndex) {
  //     // console.log(closeIndex);
  //     layui.layer.closeAll()
  // }
  window.openAddressDialog = function() {
     layui.layer.open({
         type: 2,
         title: addressType === 0 ? '@T("新增收货地址")' : '@T("新增发票地址")',
         shadeClose: true,
         shade: 0.8,
             area: ['35%', '85%'],
         content: '@Url.Action("CreateAddress", "Contact", new { Area = "Member" })' + '?addressType=' + addressType,
         success: function(layero, index) {
         //将当前窗口索引传递给iframe
         var iframe = layer.getChildFrame('body', index);
         if (iframe) {
            iframe.find('#currentLayerIndex').val(index);
         }
         },
         end: function() {
            //弹窗关闭后刷新页面
            location.reload();
         }
      });
  }
  window.openEditressDialog = function(id,addressType) {
     layui.layer.open({
         type: 2,
         title: '@T("编辑地址")',
         shadeClose: true,
         shade: 0.8,
         area: ['35%', '85%'],
             content: '@Url.Action("EditorAddress", "Contact", new { Area = "Member" })' + '?id='+id+'&addressType=' + addressType,
         success: function(layero, index) {
         //将当前窗口索引传递给iframe
         var iframe = layer.getChildFrame('body', index);
         if (iframe) {
            iframe.find('#currentLayerIndex').val(index);
         }
         },
         end: function() {
            //弹窗关闭后刷新页面
            location.reload();
         }
      });
  }
</script>