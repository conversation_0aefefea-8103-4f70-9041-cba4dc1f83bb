﻿@using B2B2CShop.Common
@using Pek.Timing
@inject IWorkContext workContext
@{
    PekHtml.AppendCssFileParts("~/public/css/pageCss/payPage.css");

    string time = UnixTime.ToDateTime(Model.AddTime).AddMinutes(15).ToString("yyyy-MM-dd HH:mm:ss");
    var currencyCode = workContext.WorkingCurrency.Code;    
    var environment = Settings.Current.Environment;
    var clientId = environment == "Sandbox" ? Settings.Current.SandboxClientId : Settings.Current.ProductionClientId;
}
<link rel="stylesheet" type="text/css" href="https://www.paypalobjects.com/webstatic/en_US/developer/docs/css/cardfields.css" />
<script src="https://www.paypal.com/sdk/js?client-id=@clientId&components=buttons&enable-funding=venmo&currency=@currencyCode&disable-funding=paylater,card"></script>
<style>
    .header1 {
        /*    width: 100%;*/
        background-color: white;
        display: flex;
        place-items: center;
    }
</style>
<div class="pageBox">
    <!-- 头部开始 -->
    <div class="header1">
        <div class="logo">
            <div>
                <img src="../../images/icons/hlk.png" class="checkOrderImage">
            </div>
            <div style="width: 1px;background-color: var(--text-color4);padding: 1.5vw 0;margin-left: 10px;"></div>
            <div style="font-size: 1.3vw;color: var(--text-color);padding-left: 20px;">@T("结算页")</div>
        </div>
        <!-- /* 步骤条 */ -->
        <div class="stepBoxContainer">
            <div class="stepBox" style="margin-bottom: 30px;">

            </div>
        </div>
    </div>
    <!-- 主体 -->
    <div class="checkOrderMain">
        <div class="left">
            <!-- 收货信息部分 -->
            <div class="box">
                <div class="flex" style="justify-content: left;">
                    <div><b>@T("订单编号"):</b> <span style="color: var(--blue-deep);">@Model.OrderSn</span></div>
                    <div style="margin-left: 30px;">
                        <b>@T("支付金额"):</b> <span class="red" style="font-size: 1.2vw;">@<EMAIL></span>
                    </div>
                </div>
                <div style="color: var(--text-color1);margin-top: 10px;">
                    @T("为保证交期，请尽快付款，支付剩余时间"): 
                    <span class="red" id="syTime">

                    </span> @T("超过时限订单将自动取消")
                </div>
            </div>

            <div class="box">
                <div style="font-size: 20px;"><b>@T("支付方式")</b></div>
                <div class="payMethodsBox">
                    <div class="payMethod flex">
                        <div><input type="radio" name="payMethod" id="payMethod1" value="PayPal"></div>
                        <label for="payMethod1">
                            <img src="../../images/icons/paypalzhifu.png" alt="">
                        </label>
                    </div>

                    <div class="payMethod flex">
                        <div><input type="radio" name="payMethod" id="payMethod2" value="ZhiFuBao"></div>
                        <label for="payMethod2">
                            <img src="../../images/icons/zhifubao.png" alt="">
                        </label>
                    </div>

                    <div class="payMethod flex">
                        <div><input type="radio" name="payMethod" id="payMethod3" value="WeiXin"></div>
                        <label for="payMethod3">
                            <img src="../../images/icons/weixinzhifu.png" alt="">
                        </label>
                    </div>
                </div>
            </div>

        </div>
        <div class="right">
            <!-- 侧边栏 -->
            <div class="aside">
                <div class="asideItem serverInfoIcon" style="color: black;">
                    @T("结算明细")
                </div>
                <div class="asideItem" style="margin-top: 10px;">
                    <div>@T("商品总价")</div>
                    <div>@<EMAIL></div>
                </div>
                <div class="asideItem">
                    <div>@T("运费价格")</div>
                    <div>@<EMAIL></div>
                </div>
                <div class="asideItem"
                     style="font-size: 16px;border-bottom: 1px solid var(--line);padding-bottom: 10px ;">
                    <div style="color: black;">@T("合计")</div>
                    <div class="red serverInfoIcon"> <b>@<EMAIL></b> </div>
                </div>
                <div class="asideItem borderB" style="margin:10px 0 15px 0;padding-bottom: 15px;">
                    <button class="button button_blue" style="width: 95%;margin: 10px auto;border-radius: 45px;" onclick="OpenPayPal()">
                        @T("确认支付")
                    </button>
                </div>
                <!--  -->
                <div class="serverInfo">
                    <div class="serverTitle" style="font-size: 15px;">@T("服务申明")</div>
                    <div class="serverTitle flex">
                        <div class="iconfont icon-sign serverInfoIcon"
                             style="margin-left: .2vw;margin-right: .2vw;"></div>
                        <div>@T("快递")</div>
                    </div>
                    <div class="serverItem flex">
                        <div class="iconfont icon-ai210"></div>
                        <div>@T("支持七天无理由退货")</div>
                    </div>
                    <div class="serverItem flex">
                        <div class="iconfont icon-ai210"></div>
                        <div>@T("如果快递丢失，支持退货")</div>
                    </div>
                    <div class="serverItem flex">
                        <div class="iconfont icon-ai210"></div>
                        <div>@T("如果快递损坏，支持退货")</div>
                    </div>
                    <div class="serverItem flex">
                        <div class="iconfont icon-ai210"></div>
                        <div>@T("支持90天内免费换货")</div>
                    </div>

                    <div class="serverTitle"> <i class="iconfont icon-secured serverInfoIcon"></i> @T("安全与隐私")</div>
                    <div class="serverItem" style="margin-top: 3px;">
                        <div>@T("安全付款:未经您的同意，我们不会与任何第三方分享您的个人信息。")</div>
                        <div style="margin-top: 3px;">@T("安全的个人资料:我们保护您的隐私，确保您的个人资料安全可靠。")</div>
                    </div>

                    <div class="serverTitle">
                        <i class="iconfont icon-money-circle serverInfoIcon"></i>
                        @T("支付安全")
                    </div>
                    <div class="serverItem" style="margin-top: 3px;">
                        <div class="paymentMethods flex" style="justify-content: left;margin-bottom: .5vw;">
                            <!-- <img src="../../images/icons/zhifubao.png" alt=""> -->
                            <img src="../../images/icons/paypal.png" alt="">
                            <img src="../../images/icons/weixin.png" alt="">
                        </div>
                        <div>@T("与受欢迎的支付合作伙伴合作，您的个人信息是安全的。")</div>
                    </div>
                </div>
                <!-- 右侧边栏 -->

                <script>
                    /** 节流函数 */
                    function throttle(func, delay) {
                        let timer = null;
                        return function (...args) {
                            if (timer === null) {
                                timer = setTimeout(() => {
                                    func.apply(this, args);
                                    timer = null;
                                }, delay);
                            }
                        };
                    }
                    function isAtFooter() {
                        const footerRect = document.querySelector('.footer').getBoundingClientRect();
                        const asideRect = document.querySelector('.aside').getBoundingClientRect();
                        return asideRect.bottom > footerRect.top && asideRect.top < footerRect.bottom;
                    }
                    function handleScroll() {
                        if (isAtFooter()) {
                            $('.aside').stop(true, true).fadeOut(300);
                        } else {
                            $('.aside').stop(true, true).fadeIn();
                        }
                    }

                    // 节流处理滚动事件
                    // const throttledScroll = throttle(handleScroll, 200);
                    // window.addEventListener('scroll', throttledScroll);
                </script>
            </div>
        </div>
    </div>
    <div class="bug"></div>

    <script>
        function next(step = 2) {
            // 01 当前步骤
            const list = $('.item');
            list.each(function (index, item) {
                if (index < step) {
                    item.classList.add('current')
                }
            })
            // 02 当前步骤对应的内容
            const stepList = $('[data-step]');
            stepList.each(function (index, item) {
                if (item.getAttribute('data-step') != step || !item.getAttribute('data-step').includes(step)) {
                    item.style.display = 'none';
                }
            })

        }
        next(3)
    </script>

</div>
<script src="~/static/js/qrcode.min.js"></script>
<script asp-location="Footer">
    var paySn = "@Model.PaySn";

    console.log("密钥：",'@clientId');

    function OpenPayPal()
    {
        var status = '@Model.OrderState';
        if(status != 10){
            layui.layer.msg('@T("订单已失效")');
            return;
        }

        var payMethod = $('input[name="payMethod"]:checked').val();
        if(payMethod == "PayPal")
        {
            if('@currencyCode' == 'CNY'){
                layui.layer.msg('@T("PayPal暂不支持人民币支付，请选择其他支付方式")');
                return;
            }

            layui.layer.open({
                title:"@T("支付")",
                type:1,
                area:'800px',
                content:'<div id="paypal-button-container" class="paypal-button-container"></div><p id="result-message"></p>',
            });
            window.paypal.Buttons({
                style:
                {
                    shape:"rect",
                    layout:"vertical",
                    color:"blue",
                    label:"paypal",
                },
                async createOrder()//创建支付订单
                {
                    if(paySn == null || paySn == "")
                    {
                        const response = await fetch("@Url.Action("CreateOrderPayPal")?Id=@Model.Id",{
                            method:"POST",
                        });
                        const orderData = await response.json();
                        if(orderData.Id)
                        {
                            $.post('@T("AddOrderPay")',{Id:@Model.Id,orderId:orderData.Id},function(res)
                            {

                            });
                            return orderData.Id;
                        }
                    }
                    else
                    {
                        return paySn;
                    }
                },
                async onApprove(data,actions)//回调函数
                {
                    const response = await fetch("@Url.Action("CaptureOrderPayPal")?orderId="+data.orderID,{
                        method:"POST",
                    });
                    const res = await response.json();
                    console.log("支付结果",res);
                    layui.layer.msg(res.msg);
                    if(res.success)
                    {
                        layui.layer.open({
                            title:false,
                            type:1,
                            area:['auto','auto'],
                            closeBtn:0,
                            content: '<div class="layui-text" style="padding: 20px; text-align: center;">'+
                                        '<i class="layui-icon layui-icon-ok" style="font-size: 80px; color: #009688;"></i><br/>' +
                                        '<h2>@T("支付成功")</h2>' +
                                        '<p>@T("可以在用户中心“订单”查看")</p>' +
                                           '<button class="layui-btn layui-btn-primary layui-btn-radius" style="margin-left: 8px;" onclick="toRouter(this)" data-link="@Url.Action("OrderDetails")?Id=@Model.Id">@T("查看订单")</button>' +
                                        '<button class="layui-btn layui-btn-normal layui-btn-radius" style="margin-top: 10px;" onclick="toRouter(this)" data-link="@Url.Action("Index")">@T("返回首页")</button>' +
                                        '</div>',
                        });
                    }
                    else
                    {
                        layui.layer.open({
                            title:false,
                            type:1,
                            area:['auto','auto'],
                            closeBtn:0,
                            content: '<div class="layui-text" style="padding: 20px; text-align: center;">'+
                                        '<i class="layui-icon layui-icon-close" style="font-size: 80px; color: #FF5722;"></i><br/>' +
                                        '<h2>@T("支付失败")</h2>' +
                                        '<p>@T("可以在用户中心“订单”查看")</p>' +
                                                 '<button class="layui-btn layui-btn-primary layui-btn-radius" style="margin-left: 8px;" onclick="toRouter(this)" data-link="@Url.Action("OrderDetails")?Id=@Model.Id">@T("查看订单")</button>' +
                                        '<button class="layui-btn layui-btn-danger layui-btn-radius" style="margin-top: 10px;" onclick="toRouter(this)" data-link="@Url.Action("Index")">@T("返回首页")</button>' +
                                        '</div>',
                        });
                    }
                }
            }).render("#paypal-button-container");
        }
        else if(payMethod == "ZhiFuBao"){
            $.post('@Url.Action("CreateOrderAli")',{Id:'@Model.Id'},function(res){
                window.location.href = res.data;
            })
        }
        else if(payMethod == "WeiXin"){
            $.post('@Url.Action("CreateOrderWx")',{Id:'@Model.Id'},function(res){
                if(res.success){
                    console.log('QRCode',QRCode);
                    const qrData = res.data;
                    layui.layer.open({
                        title: '@T("微信支付二维码")',
                        type: 1,
                        area: ['290px', '450px'],
                        closeBtn: 0,
                        content: '<div id="qrCodeContainer" style="text-align: center; padding: 20px;"><div id="qrCanvas"></div><div style="margin-top: 20px;"><button id="paymentCompleted" class="layui-btn layui-btn-normal" style="margin-left: 10px;">@T("已完成支付")</button><button id="paymentNotCompleted" class="layui-btn layui-btn-danger">@T("未完成支付")</button></div></div>',
                        success: function (){
                              new QRCode(document.getElementById('qrCanvas'),{
                                  text: qrData,
                                  width: 250,
                                  height: 250,
                                  colorDark:"#000000",
                                  colorLight:"#ffffff",
                                  correctLevel:QRCode.CorrectLevel.H
                              });
                          }
                    });
                    document.getElementById('paymentCompleted').addEventListener('click',function(){       
                        $.post('@Url.Action("QueryOrderWx")',{Id:'@Model.Id'},function(res){
                            if(res.success){
                                window.location.href = '@Url.Action("OrderDetails")?Id=@Model.Id';
                            }
                            else{
                                layui.layer.msg(res.msg);
                            }
                        })
                    })
                    document.getElementById('paymentNotCompleted').addEventListener('click',function(){
                        layui.layer.closeAll();
                    })
                }
                else{
                    layui.layer.msg(res.msg);
                }
            })
        }
        else{
            layui.layer.msg('@T("请选择支付方式")');
        }
    }

    layui.use(['util'],function()
    {
        //剩余支付时间
        var util = layui.util;
        var endTime = new Date('@time');
        var serverTime = new Date(); 

        console.log(1,endTime);
        console.log(2,serverTime);

        util.countdown(endTime,serverTime,function(date,serverTime,timer)
        {
            var str = date[0] + '@T("天") ' + date[1] + ':' +  date[2] + ':' + date[3];
            layui.$('#syTime').html(str);
        });
    });

</script>