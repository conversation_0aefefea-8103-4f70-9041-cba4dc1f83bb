﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace B2B2CShop.Entity;

/// <summary>意见反馈</summary>
public partial interface IFeedBack
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>姓名</summary>
    String? Name { get; set; }

    /// <summary>电话</summary>
    String? Phone { get; set; }

    /// <summary>邮箱</summary>
    String? Mail { get; set; }

    /// <summary>公司名称</summary>
    String? CompanyName { get; set; }

    /// <summary>主题</summary>
    String? Theme { get; set; }

    /// <summary>国家</summary>
    String? Country { get; set; }

    /// <summary>反馈内容</summary>
    String? Content { get; set; }

    /// <summary>1:手机端 2:PC端</summary>
    Int16 FType { get; set; }

    /// <summary>会员ID</summary>
    Int32 UId { get; set; }

    /// <summary>会员名称</summary>
    String UName { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }
    #endregion
}
