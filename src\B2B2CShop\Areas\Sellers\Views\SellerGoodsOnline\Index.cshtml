﻿@using B2B2CShop.Entity
@{
    ViewBag.LeftMenu = "Goods";
    ViewBag.LeftChileMenu = "GoodsOnline";

    // 页面样式
    PekHtml.AppendPageCssClassParts("html-sellergoodsonline-page");
}
@await Html.PartialAsync("_Left")
<script src="~/static/admin/js/admin.js"></script>

<div class="seller_right">
    <div class="seller_items">
        <ul>
            <li class="current"><a href="/index.php/home/<USER>/index.html">@T("出售中的商品")</a></li>
        </ul>

        @* <a href="/index.php/home/<USER>/index.html" class="dssc-btn dssc-btn-green" title="@T("发布新商品")"> @T("发布新商品")</a> *@

    </div>
    <div class="p20">
        <form method="get" action="">
            <table class="search-form">
                <tr>
                    <td>&nbsp;</td>
                    @* <th>@T("本店分类")</th>
                    <td class="w160"><select name="storegc_id" class="w150">
                            <option value="0">@T("-请选择-")</option>
                        </select></td> *@

                    <th> <select name="search_type">
                            @if (@Model.search_type == 0)
                            {
                                <option value="0" selected>@T("商品名称")</option>
                                <option value="1">@T("商家货号")</option>
                                <option value="2">@T("平台货号")</option>
                            }
                            else if (@Model.search_type == 1)
                            {
                                <option value="0">@T("商品名称")</option>
                                <option value="1" selected>@T("商家货号")</option>
                                <option value="2">@T("平台货号")</option>
                            }
                            else
                            {
                                <option value="0">@T("商品名称")</option>
                                <option value="1">@T("商家货号")</option>
                                <option value="2" selected>@T("平台货号")</option>
                            }
                        </select>
                    </th>
                    <td class="w160"><input type="text" class="text w150" name="keyword" value="@Model.keyword" /></td>
                    <td class="tc w70">
                        <input type="submit" class="submit" value="@T("搜索")" />
                    </td>
                </tr>
            </table>
        </form>


        <table class="dssc-default-table">
            <thead>
                <tr ds_type="table_header">
                    <th class="w30">&nbsp;</th>
                    <th class="w50">&nbsp;</th>
                    <th coltype="editable" column="goods_name" checker="check_required" inputwidth="230px">@T("商品名称")</th>
                    <th class="w200">@T("价格")</th>
                    <th class="w200">@T("发布时间")</th>
                    <th class="w200">@T("排序")</th>
                    <th class="w220">@T("操作")</th>
                </tr>
                <tr>
                    <td class="tc"><input type="checkbox" id="all" class="checkall" /></td>
                    <td colspan="6"><label for="all">@T("全选")</label>
                        <a href="javascript:void(0);" class="dssc-btn-mini" ds_type="batchbutton"
                        uri="@Url.Action("Unshow")" name="ids" confirm="@T("您确定要下架吗?")">@T("下架")</a>
                    </td>
                </tr>
            </thead>
            <tbody>
                @foreach (Goods product in Model.list)
                {
                    var goodsIamgePath = AlbumPic.FindByName(product.GoodsImage??"")?.Cover??"";
                    <tr>
                        <th class="tc"><input type="checkbox" class="checkitem tc" value="@product.Id" /></th>
                        <th colspan="20">@T("平台货号")：@product.Id</th>
                    </tr>
                    <tr>
                        <td class="trigger">
                            <i class="tip iconfont" dstype="ajaxGoodsList" data-comminid="@product.Id"
                               title="@T("点击展开查看此商品全部规格；规格值过多时请横向拖动区域内的滚动条进行浏览。")">&#xe6db;</i>
                        </td>
                        <td>
                            <div class="pic-thumb">
                                <a href="@Url.Action("Index","Goods",new{GoodsId = product.Id})"
                                   target="_blank"><img src="@goodsIamgePath" /></a>
                            </div>
                        </td>
                        <td class="tl">
                            <dl class="goods-name">
                                <dt style="max-width: 450px !important;">
                                    <a href="@Url.Action("Index","Goods",new {Area = "",goodsId = product.Id})" target="_blank">@product.Name</a>
                                </dt>
                                <dd>@T("商家货号")：@product.GoodsSerial</dd>
                                <dd class="serve">
                                    @if (product.GoodsCommend == 1)
                                    {
                                        <span class="open" title="@T("店铺推荐商品")"><i class="commend">@T("荐")</i></span>
                                    }
                                    <span class="" title="@T("手机端商品详情")"><i class="iconfont">&#xe601;</i></span>
                                    <span class="" title="@T("商品页面二维码")">
                                        <i class="iconfont">&#xe72d;</i>
                                        <div class="QRcode">
                                            <a target="_blank" href="@product.GoodsImage">@T("下载标签")</a>
                                            <p><img src="@product.GoodsImage" /></p>
                                        </div>
                                    </span>
                                </dd>
                            </dl>
                        </td>
                        <td class="hover" ds_type="dialog" dialog_width="720" dialog_id="edit_storage" dialog_title="@T("修改价格")"
                            uri="/index.php/home/<USER>/edit_storage.html?commonid=@product.Id">
                            <span>$ @product.GoodsPrice</span><i class="iconfont hidden">&#xe731;</i>
                        </td>
                        <td class="goods-time">@product.CreateTime.ToString("yyyy-MM-dd")</td>
                        <td>
                            <a href="javascript:void(0);" dstype="sort"
                               data-param="{urls:'/index.php/home/<USER>/edit_sort.html?commonid=@product.Id'}">
                                @product.GoodsSort<i class="iconfont">&#xe731;</i>
                            </a>
                        </td>
                        <td class="dscs-table-handle">
                            <span>
                                <a href="@Url.Action("UploadData",new{Id = product.Id})"
                                   class="btn-blue">
                                    <i class="iconfont">&#xe733;</i>
                                    <p>@T("上传资料")</p>
                                </a>
                            </span>
                            <span>
                                <a href="@Url.Action("EditGoodsDetail",new{id = product.Id})"
                                   class="btn-blue">
                                    <i class="iconfont">&#xe731;</i>
                                    <p>@T("编辑")</p>
                                </a>
                            </span>
                           @*  <span>
                                <a href="javascript:void(0);"
                                   onclick="javascript:dsLayerConfirm('@Url.Action("Delete")?ids=' + '@product.Id','@T("您确定要删除吗?")')"
                                   class="btn-red">
                                    <i class="iconfont">&#xe725;</i>
                                    <p>@T("删除")</p>
                                </a>
                            </span> *@
                        </td>
                    </tr>
                    <tr style="display:none;">
                    <td colspan="7">
                            <div class="dssc-goods-sku ps-container"></div>
                        </td>
                    </tr>
                }
            </tbody>
            <tfoot>
                
                <tr>
                    <td colspan="7">
                        <div class="pagination">
                            <ul class="pagination">
                                @Html.Raw(Model.PageHtml)
                            </ul>
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
        <script src="/static/home/<USER>/store_goods_list.js"></script>

        <script>
            $(function () {
                $('a[dstype="batch"]').click(function () {
                    if ($('.checkitem:checked').length == 0) {    //没有选择
                        layer.alert('请选择需要操作的记录');
                        return false;
                    }
                    var _items = '';
                    $('.checkitem:checked').each(function () {
                        _items += $(this).val() + ',';
                    });
                    _items = _items.substr(0, (_items.length - 1));

                    var data_str = '';
                    eval('data_str = ' + $(this).attr('data-param'));

                    if (data_str.sign == 'jingle') {
                        ajax_form('ajax_jingle', '设置广告词', data_str.urls + '?commonid=' + _items + '&inajax=1', '480');
                    } else if (data_str.sign == 'plate') {
                        ajax_form('ajax_plate', '设置关联版式', data_str.urls + '?commonid=' + _items + '&inajax=1', '480');
                    }
                });
            });
            $(function () {
                $('a[dstype="sort"]').click(function () {
                    var _items = '';
                    $('.checkitem:checked').each(function () {
                        _items = $(this).val();
                    });
                    _items = _items.substr(0, (_items.length - 1));

                    var data_str = '';
                    eval('data_str = ' + $(this).attr('data-param'));

                    ajax_form('ajax_jingle', '设置商品排序', data_str.urls + '&inajax=1', '480');
                });
            });
        </script>



    </div>
</div>